{"version": 3, "sources": ["../../src/lib/format-server-error.ts"], "sourcesContent": ["const invalidServerComponentReactHooks = [\n  'useDeferredValue',\n  'useEffect',\n  'useImperativeHandle',\n  'useInsertionEffect',\n  'useLayoutEffect',\n  'useReducer',\n  'useRef',\n  'useState',\n  'useSyncExternalStore',\n  'useTransition',\n  'experimental_useOptimistic',\n  'useOptimistic',\n]\n\nfunction setMessage(error: Error, message: string): void {\n  error.message = message\n  if (error.stack) {\n    const lines = error.stack.split('\\n')\n    lines[0] = message\n    error.stack = lines.join('\\n')\n  }\n}\n\n/**\n * Input:\n * Error: Something went wrong\n    at funcName (/path/to/file.js:10:5)\n    at anotherFunc (/path/to/file.js:15:10)\n \n * Output:\n    at funcName (/path/to/file.js:10:5)\n    at anotherFunc (/path/to/file.js:15:10) \n */\nexport function getStackWithoutErrorMessage(error: Error): string {\n  const stack = error.stack\n  if (!stack) return ''\n  return stack.replace(/^[^\\n]*\\n/, '')\n}\n\nexport function formatServerError(error: Error): void {\n  if (typeof error?.message !== 'string') return\n\n  if (\n    error.message.includes(\n      'Class extends value undefined is not a constructor or null'\n    )\n  ) {\n    const addedMessage =\n      'This might be caused by a React Class Component being rendered in a Server Component, React Class Components only works in Client Components. Read more: https://nextjs.org/docs/messages/class-component-in-server-component'\n\n    // If this error instance already has the message, don't add it again\n    if (error.message.includes(addedMessage)) return\n\n    setMessage(\n      error,\n      `${error.message}\n\n${addedMessage}`\n    )\n    return\n  }\n\n  if (error.message.includes('createContext is not a function')) {\n    setMessage(\n      error,\n      'createContext only works in Client Components. Add the \"use client\" directive at the top of the file to use it. Read more: https://nextjs.org/docs/messages/context-in-server-component'\n    )\n    return\n  }\n\n  for (const clientHook of invalidServerComponentReactHooks) {\n    const regex = new RegExp(`\\\\b${clientHook}\\\\b.*is not a function`)\n    if (regex.test(error.message)) {\n      setMessage(\n        error,\n        `${clientHook} only works in Client Components. Add the \"use client\" directive at the top of the file to use it. Read more: https://nextjs.org/docs/messages/react-client-hook-in-server-component`\n      )\n      return\n    }\n  }\n}\n"], "names": ["formatServerError", "getStackWithoutErrorMessage", "invalidServerComponentReactHooks", "setMessage", "error", "message", "stack", "lines", "split", "join", "replace", "includes", "addedMessage", "clientHook", "regex", "RegExp", "test"], "mappings": ";;;;;;;;;;;;;;;IAwCgBA,iBAAiB;eAAjBA;;IANAC,2BAA2B;eAA3BA;;;AAlChB,MAAMC,mCAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,WAAWC,KAAY,EAAEC,OAAe;IAC/CD,MAAMC,OAAO,GAAGA;IAChB,IAAID,MAAME,KAAK,EAAE;QACf,MAAMC,QAAQH,MAAME,KAAK,CAACE,KAAK,CAAC;QAChCD,KAAK,CAAC,EAAE,GAAGF;QACXD,MAAME,KAAK,GAAGC,MAAME,IAAI,CAAC;IAC3B;AACF;AAYO,SAASR,4BAA4BG,KAAY;IACtD,MAAME,QAAQF,MAAME,KAAK;IACzB,IAAI,CAACA,OAAO,OAAO;IACnB,OAAOA,MAAMI,OAAO,CAAC,aAAa;AACpC;AAEO,SAASV,kBAAkBI,KAAY;IAC5C,IAAI,QAAOA,yBAAAA,MAAOC,OAAO,MAAK,UAAU;IAExC,IACED,MAAMC,OAAO,CAACM,QAAQ,CACpB,+DAEF;QACA,MAAMC,eACJ;QAEF,qEAAqE;QACrE,IAAIR,MAAMC,OAAO,CAACM,QAAQ,CAACC,eAAe;QAE1CT,WACEC,OACA,GAAGA,MAAMC,OAAO,CAAC;;AAEvB,EAAEO,cAAc;QAEZ;IACF;IAEA,IAAIR,MAAMC,OAAO,CAACM,QAAQ,CAAC,oCAAoC;QAC7DR,WACEC,OACA;QAEF;IACF;IAEA,KAAK,MAAMS,cAAcX,iCAAkC;QACzD,MAAMY,QAAQ,IAAIC,OAAO,CAAC,GAAG,EAAEF,WAAW,sBAAsB,CAAC;QACjE,IAAIC,MAAME,IAAI,CAACZ,MAAMC,OAAO,GAAG;YAC7BF,WACEC,OACA,GAAGS,WAAW,oLAAoL,CAAC;YAErM;QACF;IACF;AACF"}