{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "sourcesContent": ["import type { RequestD<PERSON>, FetchEventResult } from './types'\nimport type { RequestInit } from './spec-extension/request'\nimport { PageSignatureError } from './error'\nimport { fromNodeOutgoingHttpHeaders, normalizeNextQueryParam } from './utils'\nimport {\n  NextFetchEvent,\n  getWaitUntilPromiseFromEvent,\n} from './spec-extension/fetch-event'\nimport { NextRequest } from './spec-extension/request'\nimport { NextResponse } from './spec-extension/response'\nimport {\n  parseRelativeURL,\n  getRelativeURL,\n} from '../../shared/lib/router/utils/relativize-url'\nimport { NextURL } from './next-url'\nimport { stripInternalSearchParams } from '../internal-utils'\nimport { normalizeRscURL } from '../../shared/lib/router/utils/app-paths'\nimport {\n  FLIGHT_HEADERS,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  RSC_HEADER,\n} from '../../client/components/app-router-headers'\nimport { ensureInstrumentationRegistered } from './globals'\nimport { createRequestStoreForAPI } from '../async-storage/request-store'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { createWorkStore } from '../async-storage/work-store'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { NEXT_ROUTER_PREFETCH_HEADER } from '../../client/components/app-router-headers'\nimport { getTracer } from '../lib/trace/tracer'\nimport type { TextMapGetter } from 'next/dist/compiled/@opentelemetry/api'\nimport { MiddlewareSpan } from '../lib/trace/constants'\nimport { CloseController } from './web-on-close'\nimport { getEdgePreviewProps } from './get-edge-preview-props'\nimport { getBuiltinRequestContext } from '../after/builtin-request-context'\nimport { getImplicitTags } from '../lib/implicit-tags'\n\nexport class NextRequestHint extends NextRequest {\n  sourcePage: string\n  fetchMetrics: FetchEventResult['fetchMetrics'] | undefined\n\n  constructor(params: {\n    init: RequestInit\n    input: Request | string\n    page: string\n  }) {\n    super(params.input, params.init)\n    this.sourcePage = params.page\n  }\n\n  get request() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  respondWith() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  waitUntil() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n}\n\nconst headersGetter: TextMapGetter<Headers> = {\n  keys: (headers) => Array.from(headers.keys()),\n  get: (headers, key) => headers.get(key) ?? undefined,\n}\n\nexport type AdapterOptions = {\n  handler: (req: NextRequestHint, event: NextFetchEvent) => Promise<Response>\n  page: string\n  request: RequestData\n  IncrementalCache?: typeof import('../lib/incremental-cache').IncrementalCache\n}\n\nlet propagator: <T>(request: NextRequestHint, fn: () => T) => T = (\n  request,\n  fn\n) => {\n  const tracer = getTracer()\n  return tracer.withPropagatedContext(request.headers, fn, headersGetter)\n}\n\nlet testApisIntercepted = false\n\nfunction ensureTestApisIntercepted() {\n  if (!testApisIntercepted) {\n    testApisIntercepted = true\n    if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n      const {\n        interceptTestApis,\n        wrapRequestHandler,\n      } = require('next/dist/experimental/testmode/server-edge')\n      interceptTestApis()\n      propagator = wrapRequestHandler(propagator)\n    }\n  }\n}\n\nexport async function adapter(\n  params: AdapterOptions\n): Promise<FetchEventResult> {\n  ensureTestApisIntercepted()\n  await ensureInstrumentationRegistered()\n\n  // TODO-APP: use explicit marker for this\n  const isEdgeRendering =\n    typeof (globalThis as any).__BUILD_MANIFEST !== 'undefined'\n\n  params.request.url = normalizeRscURL(params.request.url)\n\n  const requestURL = new NextURL(params.request.url, {\n    headers: params.request.headers,\n    nextConfig: params.request.nextConfig,\n  })\n\n  // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n  // Instead we use the keys before iteration.\n  const keys = [...requestURL.searchParams.keys()]\n  for (const key of keys) {\n    const value = requestURL.searchParams.getAll(key)\n\n    const normalizedKey = normalizeNextQueryParam(key)\n    if (normalizedKey) {\n      requestURL.searchParams.delete(normalizedKey)\n      for (const val of value) {\n        requestURL.searchParams.append(normalizedKey, val)\n      }\n      requestURL.searchParams.delete(key)\n    }\n  }\n\n  // Ensure users only see page requests, never data requests.\n  const buildId = requestURL.buildId\n  requestURL.buildId = ''\n\n  const requestHeaders = fromNodeOutgoingHttpHeaders(params.request.headers)\n  const isNextDataRequest = requestHeaders.has('x-nextjs-data')\n  const isRSCRequest = requestHeaders.get(RSC_HEADER) === '1'\n\n  if (isNextDataRequest && requestURL.pathname === '/index') {\n    requestURL.pathname = '/'\n  }\n\n  const flightHeaders = new Map()\n\n  // Headers should only be stripped for middleware\n  if (!isEdgeRendering) {\n    for (const header of FLIGHT_HEADERS) {\n      const key = header.toLowerCase()\n      const value = requestHeaders.get(key)\n      if (value !== null) {\n        flightHeaders.set(key, value)\n        requestHeaders.delete(key)\n      }\n    }\n  }\n\n  const normalizeURL = process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n    ? new URL(params.request.url)\n    : requestURL\n\n  const request = new NextRequestHint({\n    page: params.page,\n    // Strip internal query parameters off the request.\n    input: stripInternalSearchParams(normalizeURL).toString(),\n    init: {\n      body: params.request.body,\n      headers: requestHeaders,\n      method: params.request.method,\n      nextConfig: params.request.nextConfig,\n      signal: params.request.signal,\n    },\n  })\n\n  /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */\n  if (isNextDataRequest) {\n    Object.defineProperty(request, '__isData', {\n      enumerable: false,\n      value: true,\n    })\n  }\n\n  if (\n    !(globalThis as any).__incrementalCache &&\n    (params as any).IncrementalCache\n  ) {\n    ;(globalThis as any).__incrementalCache = new (\n      params as any\n    ).IncrementalCache({\n      appDir: true,\n      fetchCache: true,\n      minimalMode: process.env.NODE_ENV !== 'development',\n      fetchCacheKeyPrefix: process.env.__NEXT_FETCH_CACHE_KEY_PREFIX,\n      dev: process.env.NODE_ENV === 'development',\n      requestHeaders: params.request.headers as any,\n      requestProtocol: 'https',\n      getPrerenderManifest: () => {\n        return {\n          version: -1 as any, // letting us know this doesn't conform to spec\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          preview: getEdgePreviewProps(),\n        }\n      },\n    })\n  }\n\n  // if we're in an edge runtime sandbox, we should use the waitUntil\n  // that we receive from the enclosing NextServer\n  const outerWaitUntil =\n    params.request.waitUntil ?? getBuiltinRequestContext()?.waitUntil\n\n  const event = new NextFetchEvent({\n    request,\n    page: params.page,\n    context: outerWaitUntil ? { waitUntil: outerWaitUntil } : undefined,\n  })\n  let response\n  let cookiesFromResponse\n\n  response = await propagator(request, () => {\n    // we only care to make async storage available for middleware\n    const isMiddleware =\n      params.page === '/middleware' || params.page === '/src/middleware'\n\n    if (isMiddleware) {\n      // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n      // so we have to inject it via DefinePlugin.\n      // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n\n      const waitUntil = event.waitUntil.bind(event)\n      const closeController = new CloseController()\n\n      return getTracer().trace(\n        MiddlewareSpan.execute,\n        {\n          spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n          attributes: {\n            'http.target': request.nextUrl.pathname,\n            'http.method': request.method,\n          },\n        },\n        async () => {\n          try {\n            const onUpdateCookies = (cookies: Array<string>) => {\n              cookiesFromResponse = cookies\n            }\n            const previewProps = getEdgePreviewProps()\n            const page = '/' // Fake Work\n            const fallbackRouteParams = null\n\n            const implicitTags = await getImplicitTags(\n              page,\n              request.nextUrl,\n              fallbackRouteParams\n            )\n\n            const requestStore = createRequestStoreForAPI(\n              request,\n              request.nextUrl,\n              implicitTags,\n              onUpdateCookies,\n              previewProps\n            )\n\n            const workStore = createWorkStore({\n              page,\n              fallbackRouteParams,\n              renderOpts: {\n                cacheLifeProfiles:\n                  params.request.nextConfig?.experimental?.cacheLife,\n                experimental: {\n                  isRoutePPREnabled: false,\n                  dynamicIO: false,\n                  authInterrupts:\n                    !!params.request.nextConfig?.experimental?.authInterrupts,\n                },\n                supportsDynamicResponse: true,\n                waitUntil,\n                onClose: closeController.onClose.bind(closeController),\n                onAfterTaskError: undefined,\n              },\n              requestEndedState: { ended: false },\n              isPrefetchRequest: request.headers.has(\n                NEXT_ROUTER_PREFETCH_HEADER\n              ),\n              buildId: buildId ?? '',\n              previouslyRevalidatedTags: [],\n            })\n\n            return await workAsyncStorage.run(workStore, () =>\n              workUnitAsyncStorage.run(\n                requestStore,\n                params.handler,\n                request,\n                event\n              )\n            )\n          } finally {\n            // middleware cannot stream, so we can consider the response closed\n            // as soon as the handler returns.\n            // we can delay running it until a bit later --\n            // if it's needed, we'll have a `waitUntil` lock anyway.\n            setTimeout(() => {\n              closeController.dispatchClose()\n            }, 0)\n          }\n        }\n      )\n    }\n    return params.handler(request, event)\n  })\n\n  // check if response is a Response object\n  if (response && !(response instanceof Response)) {\n    throw new TypeError('Expected an instance of Response to be returned')\n  }\n\n  if (response && cookiesFromResponse) {\n    response.headers.set('set-cookie', cookiesFromResponse)\n  }\n\n  /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */\n  const rewrite = response?.headers.get('x-middleware-rewrite')\n  if (response && rewrite && (isRSCRequest || !isEdgeRendering)) {\n    const destination = new NextURL(rewrite, {\n      forceLocale: true,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE && !isEdgeRendering) {\n      if (destination.host === request.nextUrl.host) {\n        destination.buildId = buildId || destination.buildId\n        response.headers.set('x-middleware-rewrite', String(destination))\n      }\n    }\n\n    /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */\n    const { url: relativeDestination, isRelative } = parseRelativeURL(\n      destination.toString(),\n      requestURL.toString()\n    )\n\n    if (\n      !isEdgeRendering &&\n      isNextDataRequest &&\n      // if the rewrite is external and external rewrite\n      // resolving config is enabled don't add this header\n      // so the upstream app can set it instead\n      !(\n        process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE &&\n        relativeDestination.match(/http(s)?:\\/\\//)\n      )\n    ) {\n      response.headers.set('x-nextjs-rewrite', relativeDestination)\n    }\n\n    // If this is an RSC request, and the pathname or search has changed, and\n    // this isn't an external rewrite, we need to set the rewritten pathname and\n    // query headers.\n    if (isRSCRequest && isRelative) {\n      if (requestURL.pathname !== destination.pathname) {\n        response.headers.set(NEXT_REWRITTEN_PATH_HEADER, destination.pathname)\n      }\n      if (requestURL.search !== destination.search) {\n        response.headers.set(\n          NEXT_REWRITTEN_QUERY_HEADER,\n          // remove the leading ? from the search string\n          destination.search.slice(1)\n        )\n      }\n    }\n  }\n\n  /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */\n  const redirect = response?.headers.get('Location')\n  if (response && redirect && !isEdgeRendering) {\n    const redirectURL = new NextURL(redirect, {\n      forceLocale: false,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */\n    response = new Response(response.body, response)\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n      if (redirectURL.host === requestURL.host) {\n        redirectURL.buildId = buildId || redirectURL.buildId\n        response.headers.set('Location', redirectURL.toString())\n      }\n    }\n\n    /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */\n    if (isNextDataRequest) {\n      response.headers.delete('Location')\n      response.headers.set(\n        'x-nextjs-redirect',\n        getRelativeURL(redirectURL.toString(), requestURL.toString())\n      )\n    }\n  }\n\n  const finalResponse = response ? response : NextResponse.next()\n\n  // Flight headers are not overridable / removable so they are applied at the end.\n  const middlewareOverrideHeaders = finalResponse.headers.get(\n    'x-middleware-override-headers'\n  )\n  const overwrittenHeaders: string[] = []\n  if (middlewareOverrideHeaders) {\n    for (const [key, value] of flightHeaders) {\n      finalResponse.headers.set(`x-middleware-request-${key}`, value)\n      overwrittenHeaders.push(key)\n    }\n\n    if (overwrittenHeaders.length > 0) {\n      finalResponse.headers.set(\n        'x-middleware-override-headers',\n        middlewareOverrideHeaders + ',' + overwrittenHeaders.join(',')\n      )\n    }\n  }\n\n  return {\n    response: finalResponse,\n    waitUntil: getWaitUntilPromiseFromEvent(event) ?? Promise.resolve(),\n    fetchMetrics: request.fetchMetrics,\n  }\n}\n"], "names": ["NextRequestHint", "adapter", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "getTracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "getBuiltinRequestContext", "ensureInstrumentationRegistered", "isEdgeRendering", "globalThis", "__BUILD_MANIFEST", "url", "normalizeRscURL", "requestURL", "NextURL", "nextConfig", "searchParams", "value", "getAll", "normalizedKey", "normalizeNextQueryParam", "delete", "val", "append", "buildId", "requestHeaders", "fromNodeOutgoingHttpHeaders", "isNextDataRequest", "has", "isRSCRequest", "RSC_HEADER", "pathname", "flightHeaders", "Map", "header", "FLIGHT_HEADERS", "toLowerCase", "set", "normalizeURL", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "stripInternalSearchParams", "toString", "body", "method", "signal", "Object", "defineProperty", "enumerable", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getEdgePreviewProps", "outerWaitUntil", "event", "NextFetchEvent", "context", "response", "cookiesFromResponse", "isMiddleware", "bind", "closeController", "CloseController", "trace", "MiddlewareSpan", "execute", "spanName", "nextUrl", "attributes", "onUpdateCookies", "cookies", "previewProps", "fallbackRouteParams", "implicitTags", "getImplicitTags", "requestStore", "createRequestStoreForAPI", "workStore", "createWorkStore", "renderOpts", "cacheLifeProfiles", "experimental", "cacheLife", "isRoutePPREnabled", "dynamicIO", "authInterrupts", "supportsDynamicResponse", "onClose", "onAfterTaskError", "requestEndedState", "ended", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "previouslyRevalidatedTags", "workAsyncStorage", "run", "workUnitAsyncStorage", "handler", "setTimeout", "dispatchClose", "Response", "TypeError", "rewrite", "destination", "forceLocale", "host", "String", "relativeDestination", "isRelative", "parseRelativeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "NEXT_REWRITTEN_PATH_HEADER", "search", "NEXT_REWRITTEN_QUERY_HEADER", "slice", "redirect", "redirectURL", "getRelativeURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "getWaitUntilPromiseFromEvent", "Promise", "resolve", "fetchMetrics"], "mappings": ";;;;;;;;;;;;;;;IAqCaA,eAAe;eAAfA;;IA8DSC,OAAO;eAAPA;;;uBAjGa;uBACkC;4BAI9D;yBACqB;0BACC;+BAItB;yBACiB;+BACkB;0BACV;kCAMzB;yBACyC;8BACP;8CACJ;2BACL;0CACC;wBAEP;2BAEK;4BACC;qCACI;uCACK;8BACT;AAEzB,MAAMD,wBAAwBE,oBAAW;IAI9CC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,qBAAiD,CAAjD,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAI,cAAc;QACZ,MAAM,qBAAiD,CAAjD,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAK,YAAY;QACV,MAAM,qBAAiD,CAAjD,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;AACF;AAEA,MAAMM,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEZ,SACAa;IAEA,MAAMC,SAASC,IAAAA,iBAAS;IACxB,OAAOD,OAAOE,qBAAqB,CAAChB,QAAQM,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIa,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAV,aAAaW,mBAAmBX;QAClC;IACF;AACF;AAEO,eAAepB,QACpBG,MAAsB;QAoHQ8B;IAlH9BP;IACA,MAAMQ,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBACJ,OAAO,AAACC,WAAmBC,gBAAgB,KAAK;IAElDlC,OAAOK,OAAO,CAAC8B,GAAG,GAAGC,IAAAA,yBAAe,EAACpC,OAAOK,OAAO,CAAC8B,GAAG;IAEvD,MAAME,aAAa,IAAIC,gBAAO,CAACtC,OAAOK,OAAO,CAAC8B,GAAG,EAAE;QACjDxB,SAASX,OAAOK,OAAO,CAACM,OAAO;QAC/B4B,YAAYvC,OAAOK,OAAO,CAACkC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM7B,OAAO;WAAI2B,WAAWG,YAAY,CAAC9B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM+B,QAAQJ,WAAWG,YAAY,CAACE,MAAM,CAAC3B;QAE7C,MAAM4B,gBAAgBC,IAAAA,8BAAuB,EAAC7B;QAC9C,IAAI4B,eAAe;YACjBN,WAAWG,YAAY,CAACK,MAAM,CAACF;YAC/B,KAAK,MAAMG,OAAOL,MAAO;gBACvBJ,WAAWG,YAAY,CAACO,MAAM,CAACJ,eAAeG;YAChD;YACAT,WAAWG,YAAY,CAACK,MAAM,CAAC9B;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMiC,UAAUX,WAAWW,OAAO;IAClCX,WAAWW,OAAO,GAAG;IAErB,MAAMC,iBAAiBC,IAAAA,kCAA2B,EAAClD,OAAOK,OAAO,CAACM,OAAO;IACzE,MAAMwC,oBAAoBF,eAAeG,GAAG,CAAC;IAC7C,MAAMC,eAAeJ,eAAenC,GAAG,CAACwC,4BAAU,MAAM;IAExD,IAAIH,qBAAqBd,WAAWkB,QAAQ,KAAK,UAAU;QACzDlB,WAAWkB,QAAQ,GAAG;IACxB;IAEA,MAAMC,gBAAgB,IAAIC;IAE1B,iDAAiD;IACjD,IAAI,CAACzB,iBAAiB;QACpB,KAAK,MAAM0B,UAAUC,gCAAc,CAAE;YACnC,MAAM5C,MAAM2C,OAAOE,WAAW;YAC9B,MAAMnB,QAAQQ,eAAenC,GAAG,CAACC;YACjC,IAAI0B,UAAU,MAAM;gBAClBe,cAAcK,GAAG,CAAC9C,KAAK0B;gBACvBQ,eAAeJ,MAAM,CAAC9B;YACxB;QACF;IACF;IAEA,MAAM+C,eAAetC,QAAQC,GAAG,CAACsC,kCAAkC,GAC/D,IAAIC,IAAIhE,OAAOK,OAAO,CAAC8B,GAAG,IAC1BE;IAEJ,MAAMhC,UAAU,IAAIT,gBAAgB;QAClCQ,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOgE,IAAAA,wCAAyB,EAACH,cAAcI,QAAQ;QACvDhE,MAAM;YACJiE,MAAMnE,OAAOK,OAAO,CAAC8D,IAAI;YACzBxD,SAASsC;YACTmB,QAAQpE,OAAOK,OAAO,CAAC+D,MAAM;YAC7B7B,YAAYvC,OAAOK,OAAO,CAACkC,UAAU;YACrC8B,QAAQrE,OAAOK,OAAO,CAACgE,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIlB,mBAAmB;QACrBmB,OAAOC,cAAc,CAAClE,SAAS,YAAY;YACzCmE,YAAY;YACZ/B,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACR,WAAmBwC,kBAAkB,IACvC,AAACzE,OAAe0E,gBAAgB,EAChC;;QACEzC,WAAmBwC,kBAAkB,GAAG,IAAI,AAC5CzE,OACA0E,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAarD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YACtCC,qBAAqBvD,QAAQC,GAAG,CAACuD,6BAA6B;YAC9DC,KAAKzD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YAC9B7B,gBAAgBjD,OAAOK,OAAO,CAACM,OAAO;YACtCuE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASC,IAAAA,wCAAmB;gBAC9B;YACF;QACF;IACF;IAEA,mEAAmE;IACnE,gDAAgD;IAChD,MAAMC,iBACJ1F,OAAOK,OAAO,CAACG,SAAS,MAAIsB,4BAAAA,IAAAA,+CAAwB,wBAAxBA,0BAA4BtB,SAAS;IAEnE,MAAMmF,QAAQ,IAAIC,0BAAc,CAAC;QAC/BvF;QACAD,MAAMJ,OAAOI,IAAI;QACjByF,SAASH,iBAAiB;YAAElF,WAAWkF;QAAe,IAAI1E;IAC5D;IACA,IAAI8E;IACJ,IAAIC;IAEJD,WAAW,MAAM7E,WAAWZ,SAAS;QACnC,8DAA8D;QAC9D,MAAM2F,eACJhG,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QAEnD,IAAI4F,cAAc;YAChB,0FAA0F;YAC1F,4CAA4C;YAC5C,qFAAqF;YAErF,MAAMxF,YAAYmF,MAAMnF,SAAS,CAACyF,IAAI,CAACN;YACvC,MAAMO,kBAAkB,IAAIC,2BAAe;YAE3C,OAAO/E,IAAAA,iBAAS,IAAGgF,KAAK,CACtBC,yBAAc,CAACC,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAElG,QAAQ+D,MAAM,CAAC,CAAC,EAAE/D,QAAQmG,OAAO,CAACjD,QAAQ,EAAE;gBACpEkD,YAAY;oBACV,eAAepG,QAAQmG,OAAO,CAACjD,QAAQ;oBACvC,eAAelD,QAAQ+D,MAAM;gBAC/B;YACF,GACA;gBACE,IAAI;wBA2BIpE,yCAAAA,4BAKIA,0CAAAA;oBA/BV,MAAM0G,kBAAkB,CAACC;wBACvBZ,sBAAsBY;oBACxB;oBACA,MAAMC,eAAenB,IAAAA,wCAAmB;oBACxC,MAAMrF,OAAO,IAAI,YAAY;;oBAC7B,MAAMyG,sBAAsB;oBAE5B,MAAMC,eAAe,MAAMC,IAAAA,6BAAe,EACxC3G,MACAC,QAAQmG,OAAO,EACfK;oBAGF,MAAMG,eAAeC,IAAAA,sCAAwB,EAC3C5G,SACAA,QAAQmG,OAAO,EACfM,cACAJ,iBACAE;oBAGF,MAAMM,YAAYC,IAAAA,0BAAe,EAAC;wBAChC/G;wBACAyG;wBACAO,YAAY;4BACVC,iBAAiB,GACfrH,6BAAAA,OAAOK,OAAO,CAACkC,UAAU,sBAAzBvC,0CAAAA,2BAA2BsH,YAAY,qBAAvCtH,wCAAyCuH,SAAS;4BACpDD,cAAc;gCACZE,mBAAmB;gCACnBC,WAAW;gCACXC,gBACE,CAAC,GAAC1H,8BAAAA,OAAOK,OAAO,CAACkC,UAAU,sBAAzBvC,2CAAAA,4BAA2BsH,YAAY,qBAAvCtH,yCAAyC0H,cAAc;4BAC7D;4BACAC,yBAAyB;4BACzBnH;4BACAoH,SAAS1B,gBAAgB0B,OAAO,CAAC3B,IAAI,CAACC;4BACtC2B,kBAAkB7G;wBACpB;wBACA8G,mBAAmB;4BAAEC,OAAO;wBAAM;wBAClCC,mBAAmB3H,QAAQM,OAAO,CAACyC,GAAG,CACpC6E,6CAA2B;wBAE7BjF,SAASA,WAAW;wBACpBkF,2BAA2B,EAAE;oBAC/B;oBAEA,OAAO,MAAMC,0CAAgB,CAACC,GAAG,CAAClB,WAAW,IAC3CmB,kDAAoB,CAACD,GAAG,CACtBpB,cACAhH,OAAOsI,OAAO,EACdjI,SACAsF;gBAGN,SAAU;oBACR,mEAAmE;oBACnE,kCAAkC;oBAClC,+CAA+C;oBAC/C,wDAAwD;oBACxD4C,WAAW;wBACTrC,gBAAgBsC,aAAa;oBAC/B,GAAG;gBACL;YACF;QAEJ;QACA,OAAOxI,OAAOsI,OAAO,CAACjI,SAASsF;IACjC;IAEA,yCAAyC;IACzC,IAAIG,YAAY,CAAEA,CAAAA,oBAAoB2C,QAAO,GAAI;QAC/C,MAAM,qBAAgE,CAAhE,IAAIC,UAAU,oDAAd,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAI5C,YAAYC,qBAAqB;QACnCD,SAASnF,OAAO,CAACkD,GAAG,CAAC,cAAckC;IACrC;IAEA;;;;;GAKC,GACD,MAAM4C,UAAU7C,4BAAAA,SAAUnF,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIgF,YAAY6C,WAAYtF,CAAAA,gBAAgB,CAACrB,eAAc,GAAI;QAC7D,MAAM4G,cAAc,IAAItG,gBAAO,CAACqG,SAAS;YACvCE,aAAa;YACblI,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B4B,YAAYvC,OAAOK,OAAO,CAACkC,UAAU;QACvC;QAEA,IAAI,CAACf,QAAQC,GAAG,CAACsC,kCAAkC,IAAI,CAAC/B,iBAAiB;YACvE,IAAI4G,YAAYE,IAAI,KAAKzI,QAAQmG,OAAO,CAACsC,IAAI,EAAE;gBAC7CF,YAAY5F,OAAO,GAAGA,WAAW4F,YAAY5F,OAAO;gBACpD8C,SAASnF,OAAO,CAACkD,GAAG,CAAC,wBAAwBkF,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAM,EAAEzG,KAAK6G,mBAAmB,EAAEC,UAAU,EAAE,GAAGC,IAAAA,+BAAgB,EAC/DN,YAAY1E,QAAQ,IACpB7B,WAAW6B,QAAQ;QAGrB,IACE,CAAClC,mBACDmB,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE3B,CAAAA,QAAQC,GAAG,CAAC0H,0CAA0C,IACtDH,oBAAoBI,KAAK,CAAC,gBAAe,GAE3C;YACAtD,SAASnF,OAAO,CAACkD,GAAG,CAAC,oBAAoBmF;QAC3C;QAEA,yEAAyE;QACzE,4EAA4E;QAC5E,iBAAiB;QACjB,IAAI3F,gBAAgB4F,YAAY;YAC9B,IAAI5G,WAAWkB,QAAQ,KAAKqF,YAAYrF,QAAQ,EAAE;gBAChDuC,SAASnF,OAAO,CAACkD,GAAG,CAACwF,4CAA0B,EAAET,YAAYrF,QAAQ;YACvE;YACA,IAAIlB,WAAWiH,MAAM,KAAKV,YAAYU,MAAM,EAAE;gBAC5CxD,SAASnF,OAAO,CAACkD,GAAG,CAClB0F,6CAA2B,EAC3B,8CAA8C;gBAC9CX,YAAYU,MAAM,CAACE,KAAK,CAAC;YAE7B;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMC,WAAW3D,4BAAAA,SAAUnF,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIgF,YAAY2D,YAAY,CAACzH,iBAAiB;QAC5C,MAAM0H,cAAc,IAAIpH,gBAAO,CAACmH,UAAU;YACxCZ,aAAa;YACblI,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B4B,YAAYvC,OAAOK,OAAO,CAACkC,UAAU;QACvC;QAEA;;;KAGC,GACDuD,WAAW,IAAI2C,SAAS3C,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAACtE,QAAQC,GAAG,CAACsC,kCAAkC,EAAE;YACnD,IAAI2F,YAAYZ,IAAI,KAAKzG,WAAWyG,IAAI,EAAE;gBACxCY,YAAY1G,OAAO,GAAGA,WAAW0G,YAAY1G,OAAO;gBACpD8C,SAASnF,OAAO,CAACkD,GAAG,CAAC,YAAY6F,YAAYxF,QAAQ;YACvD;QACF;QAEA;;;;KAIC,GACD,IAAIf,mBAAmB;YACrB2C,SAASnF,OAAO,CAACkC,MAAM,CAAC;YACxBiD,SAASnF,OAAO,CAACkD,GAAG,CAClB,qBACA8F,IAAAA,6BAAc,EAACD,YAAYxF,QAAQ,IAAI7B,WAAW6B,QAAQ;QAE9D;IACF;IAEA,MAAM0F,gBAAgB9D,WAAWA,WAAW+D,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAcjJ,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMkJ,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAAChJ,KAAK0B,MAAM,IAAIe,cAAe;YACxCoG,cAAcjJ,OAAO,CAACkD,GAAG,CAAC,CAAC,qBAAqB,EAAE9C,KAAK,EAAE0B;YACzDuH,mBAAmBC,IAAI,CAAClJ;QAC1B;QAEA,IAAIiJ,mBAAmBE,MAAM,GAAG,GAAG;YACjCN,cAAcjJ,OAAO,CAACkD,GAAG,CACvB,iCACAkG,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLrE,UAAU8D;QACVpJ,WAAW4J,IAAAA,wCAA4B,EAACzE,UAAU0E,QAAQC,OAAO;QACjEC,cAAclK,QAAQkK,YAAY;IACpC;AACF"}