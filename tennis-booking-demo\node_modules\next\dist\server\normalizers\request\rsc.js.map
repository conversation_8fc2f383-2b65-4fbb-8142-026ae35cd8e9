{"version": 3, "sources": ["../../../../src/server/normalizers/request/rsc.ts"], "sourcesContent": ["import type { PathnameNormalizer } from './pathname-normalizer'\n\nimport { RSC_SUFFIX } from '../../../lib/constants'\nimport { SuffixPathnameNormalizer } from './suffix'\n\nexport class RSCPathnameNormalizer\n  extends SuffixPathnameNormalizer\n  implements PathnameNormalizer\n{\n  constructor() {\n    super(RSC_SUFFIX)\n  }\n}\n"], "names": ["RSCPathnameNormalizer", "SuffixPathnameNormalizer", "constructor", "RSC_SUFFIX"], "mappings": ";;;;+BAKaA;;;eAAAA;;;2BAHc;wBACc;AAElC,MAAMA,8BACHC,gCAAwB;IAGhCC,aAAc;QACZ,KAAK,CAACC,qBAAU;IAClB;AACF"}