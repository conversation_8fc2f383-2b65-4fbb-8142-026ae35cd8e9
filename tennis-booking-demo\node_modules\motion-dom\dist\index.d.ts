interface SVGAttributes {
    accentHeight?: number | string | undefined;
    accumulate?: "none" | "sum" | undefined;
    additive?: "replace" | "sum" | undefined;
    alignmentBaseline?: "auto" | "baseline" | "before-edge" | "text-before-edge" | "middle" | "central" | "after-edge" | "text-after-edge" | "ideographic" | "alphabetic" | "hanging" | "mathematical" | "inherit" | undefined;
    allowReorder?: "no" | "yes" | undefined;
    alphabetic?: number | string | undefined;
    amplitude?: number | string | undefined;
    arabicForm?: "initial" | "medial" | "terminal" | "isolated" | undefined;
    ascent?: number | string | undefined;
    attributeName?: string | undefined;
    attributeType?: string | undefined;
    autoReverse?: boolean | undefined;
    azimuth?: number | string | undefined;
    baseFrequency?: number | string | undefined;
    baselineShift?: number | string | undefined;
    baseProfile?: number | string | undefined;
    bbox?: number | string | undefined;
    begin?: number | string | undefined;
    bias?: number | string | undefined;
    by?: number | string | undefined;
    calcMode?: number | string | undefined;
    capHeight?: number | string | undefined;
    clip?: number | string | undefined;
    clipPath?: string | undefined;
    clipPathUnits?: number | string | undefined;
    clipRule?: number | string | undefined;
    colorInterpolation?: number | string | undefined;
    colorInterpolationFilters?: "auto" | "sRGB" | "linearRGB" | "inherit" | undefined;
    colorProfile?: number | string | undefined;
    colorRendering?: number | string | undefined;
    contentScriptType?: number | string | undefined;
    contentStyleType?: number | string | undefined;
    cursor?: number | string | undefined;
    cx?: number | string | undefined;
    cy?: number | string | undefined;
    d?: string | undefined;
    decelerate?: number | string | undefined;
    descent?: number | string | undefined;
    diffuseConstant?: number | string | undefined;
    direction?: number | string | undefined;
    display?: number | string | undefined;
    divisor?: number | string | undefined;
    dominantBaseline?: number | string | undefined;
    dur?: number | string | undefined;
    dx?: number | string | undefined;
    dy?: number | string | undefined;
    edgeMode?: number | string | undefined;
    elevation?: number | string | undefined;
    enableBackground?: number | string | undefined;
    end?: number | string | undefined;
    exponent?: number | string | undefined;
    externalResourcesRequired?: boolean | undefined;
    fill?: string | undefined;
    fillOpacity?: number | string | undefined;
    fillRule?: "nonzero" | "evenodd" | "inherit" | undefined;
    filter?: string | undefined;
    filterRes?: number | string | undefined;
    filterUnits?: number | string | undefined;
    floodColor?: number | string | undefined;
    floodOpacity?: number | string | undefined;
    focusable?: boolean | "auto" | undefined;
    fontFamily?: string | undefined;
    fontSize?: number | string | undefined;
    fontSizeAdjust?: number | string | undefined;
    fontStretch?: number | string | undefined;
    fontStyle?: number | string | undefined;
    fontVariant?: number | string | undefined;
    fontWeight?: number | string | undefined;
    format?: number | string | undefined;
    fr?: number | string | undefined;
    from?: number | string | undefined;
    fx?: number | string | undefined;
    fy?: number | string | undefined;
    g1?: number | string | undefined;
    g2?: number | string | undefined;
    glyphName?: number | string | undefined;
    glyphOrientationHorizontal?: number | string | undefined;
    glyphOrientationVertical?: number | string | undefined;
    glyphRef?: number | string | undefined;
    gradientTransform?: string | undefined;
    gradientUnits?: string | undefined;
    hanging?: number | string | undefined;
    horizAdvX?: number | string | undefined;
    horizOriginX?: number | string | undefined;
    href?: string | undefined;
    ideographic?: number | string | undefined;
    imageRendering?: number | string | undefined;
    in2?: number | string | undefined;
    in?: string | undefined;
    intercept?: number | string | undefined;
    k1?: number | string | undefined;
    k2?: number | string | undefined;
    k3?: number | string | undefined;
    k4?: number | string | undefined;
    k?: number | string | undefined;
    kernelMatrix?: number | string | undefined;
    kernelUnitLength?: number | string | undefined;
    kerning?: number | string | undefined;
    keyPoints?: number | string | undefined;
    keySplines?: number | string | undefined;
    keyTimes?: number | string | undefined;
    lengthAdjust?: number | string | undefined;
    letterSpacing?: number | string | undefined;
    lightingColor?: number | string | undefined;
    limitingConeAngle?: number | string | undefined;
    local?: number | string | undefined;
    markerEnd?: string | undefined;
    markerHeight?: number | string | undefined;
    markerMid?: string | undefined;
    markerStart?: string | undefined;
    markerUnits?: number | string | undefined;
    markerWidth?: number | string | undefined;
    mask?: string | undefined;
    maskContentUnits?: number | string | undefined;
    maskUnits?: number | string | undefined;
    mathematical?: number | string | undefined;
    mode?: number | string | undefined;
    numOctaves?: number | string | undefined;
    offset?: number | string | undefined;
    opacity?: number | string | undefined;
    operator?: number | string | undefined;
    order?: number | string | undefined;
    orient?: number | string | undefined;
    orientation?: number | string | undefined;
    origin?: number | string | undefined;
    overflow?: number | string | undefined;
    overlinePosition?: number | string | undefined;
    overlineThickness?: number | string | undefined;
    paintOrder?: number | string | undefined;
    panose1?: number | string | undefined;
    path?: string | undefined;
    pathLength?: number | string | undefined;
    patternContentUnits?: string | undefined;
    patternTransform?: number | string | undefined;
    patternUnits?: string | undefined;
    pointerEvents?: number | string | undefined;
    points?: string | undefined;
    pointsAtX?: number | string | undefined;
    pointsAtY?: number | string | undefined;
    pointsAtZ?: number | string | undefined;
    preserveAlpha?: boolean | undefined;
    preserveAspectRatio?: string | undefined;
    primitiveUnits?: number | string | undefined;
    r?: number | string | undefined;
    radius?: number | string | undefined;
    refX?: number | string | undefined;
    refY?: number | string | undefined;
    renderingIntent?: number | string | undefined;
    repeatCount?: number | string | undefined;
    repeatDur?: number | string | undefined;
    requiredExtensions?: number | string | undefined;
    requiredFeatures?: number | string | undefined;
    restart?: number | string | undefined;
    result?: string | undefined;
    rotate?: number | string | undefined;
    rx?: number | string | undefined;
    ry?: number | string | undefined;
    scale?: number | string | undefined;
    seed?: number | string | undefined;
    shapeRendering?: number | string | undefined;
    slope?: number | string | undefined;
    spacing?: number | string | undefined;
    specularConstant?: number | string | undefined;
    specularExponent?: number | string | undefined;
    speed?: number | string | undefined;
    spreadMethod?: string | undefined;
    startOffset?: number | string | undefined;
    stdDeviation?: number | string | undefined;
    stemh?: number | string | undefined;
    stemv?: number | string | undefined;
    stitchTiles?: number | string | undefined;
    stopColor?: string | undefined;
    stopOpacity?: number | string | undefined;
    strikethroughPosition?: number | string | undefined;
    strikethroughThickness?: number | string | undefined;
    string?: number | string | undefined;
    stroke?: string | undefined;
    strokeDasharray?: string | number | undefined;
    strokeDashoffset?: string | number | undefined;
    strokeLinecap?: "butt" | "round" | "square" | "inherit" | undefined;
    strokeLinejoin?: "miter" | "round" | "bevel" | "inherit" | undefined;
    strokeMiterlimit?: number | string | undefined;
    strokeOpacity?: number | string | undefined;
    strokeWidth?: number | string | undefined;
    surfaceScale?: number | string | undefined;
    systemLanguage?: number | string | undefined;
    tableValues?: number | string | undefined;
    targetX?: number | string | undefined;
    targetY?: number | string | undefined;
    textAnchor?: string | undefined;
    textDecoration?: number | string | undefined;
    textLength?: number | string | undefined;
    textRendering?: number | string | undefined;
    to?: number | string | undefined;
    transform?: string | undefined;
    u1?: number | string | undefined;
    u2?: number | string | undefined;
    underlinePosition?: number | string | undefined;
    underlineThickness?: number | string | undefined;
    unicode?: number | string | undefined;
    unicodeBidi?: number | string | undefined;
    unicodeRange?: number | string | undefined;
    unitsPerEm?: number | string | undefined;
    vAlphabetic?: number | string | undefined;
    values?: string | undefined;
    vectorEffect?: number | string | undefined;
    version?: string | undefined;
    vertAdvY?: number | string | undefined;
    vertOriginX?: number | string | undefined;
    vertOriginY?: number | string | undefined;
    vHanging?: number | string | undefined;
    vIdeographic?: number | string | undefined;
    viewBox?: string | undefined;
    viewTarget?: number | string | undefined;
    visibility?: number | string | undefined;
    vMathematical?: number | string | undefined;
    widths?: number | string | undefined;
    wordSpacing?: number | string | undefined;
    writingMode?: number | string | undefined;
    x1?: number | string | undefined;
    x2?: number | string | undefined;
    x?: number | string | undefined;
    xChannelSelector?: string | undefined;
    xHeight?: number | string | undefined;
    xlinkActuate?: string | undefined;
    xlinkArcrole?: string | undefined;
    xlinkHref?: string | undefined;
    xlinkRole?: string | undefined;
    xlinkShow?: string | undefined;
    xlinkTitle?: string | undefined;
    xlinkType?: string | undefined;
    xmlBase?: string | undefined;
    xmlLang?: string | undefined;
    xmlns?: string | undefined;
    xmlnsXlink?: string | undefined;
    xmlSpace?: string | undefined;
    y1?: number | string | undefined;
    y2?: number | string | undefined;
    y?: number | string | undefined;
    yChannelSelector?: string | undefined;
    z?: number | string | undefined;
    zoomAndPan?: string | undefined;
}

interface ProgressTimeline {
    currentTime: null | {
        value: number;
    };
    cancel?: VoidFunction;
}
/**
 * Methods to control an animation.
 */
interface AnimationPlaybackControls {
    /**
     * The current time of the animation, in seconds.
     */
    time: number;
    /**
     * The playback speed of the animation.
     * 1 = normal speed, 2 = double speed, 0.5 = half speed.
     */
    speed: number;
    /**
     * The start time of the animation, in milliseconds.
     */
    startTime: number | null;
    /**
     * The state of the animation.
     *
     * This is currently for internal use only.
     */
    state?: AnimationPlayState;
    duration: number;
    /**
     * Stops the animation at its current state, and prevents it from
     * resuming when the animation is played again.
     */
    stop: () => void;
    /**
     * Plays the animation.
     */
    play: () => void;
    /**
     * Pauses the animation.
     */
    pause: () => void;
    /**
     * Completes the animation and applies the final state.
     */
    complete: () => void;
    /**
     * Cancels the animation and applies the initial state.
     */
    cancel: () => void;
    /**
     * Allows the animation to be awaited.
     */
    then: (onResolve: VoidFunction, onReject?: VoidFunction) => Promise<void>;
    /**
     * Attaches a timeline to the animation, for instance the `ScrollTimeline`.
     *
     * This is currently for internal use only.
     */
    attachTimeline?: (timeline: ProgressTimeline, fallback?: (animation: AnimationPlaybackControls | AnimationPlaybackControlsWithFinished) => VoidFunction) => VoidFunction;
    /**
     * Flattens the animation's easing curve to linear.
     *
     * This is currently for internal use only, and is used by scroll() to
     * ensure an animation is being scrubbed by progress rather than eased time.
     */
    flatten: () => void;
}
type AnimationPlaybackControlsWithFinished = Omit<AnimationPlaybackControls, "then"> & {
    finished: Promise<any>;
};
interface AnimationState<V> {
    value: V;
    done: boolean;
}
interface KeyframeGenerator<V> {
    calculatedDuration: null | number;
    next: (t: number) => AnimationState<V>;
    toString: () => string;
}
interface ValueAnimationOptions<V extends string | number = number> extends ValueAnimationTransition {
    keyframes: V[];
    name?: string;
    from?: V;
    isGenerator?: boolean;
}
type GeneratorFactory = (options: ValueAnimationOptions<any>) => KeyframeGenerator<any>;
type AnimationGeneratorType = GeneratorFactory | "decay" | "spring" | "keyframes" | "tween" | "inertia";
interface AnimationPlaybackLifecycles<V> {
    onUpdate?: (latest: V) => void;
    onPlay?: () => void;
    onComplete?: () => void;
    onRepeat?: () => void;
    onStop?: () => void;
}
interface ValueAnimationTransition<V = any> extends Transition, AnimationPlaybackLifecycles<V> {
}
type RepeatType = "loop" | "reverse" | "mirror";
interface AnimationPlaybackOptions {
    repeat?: number;
    repeatType?: RepeatType;
    repeatDelay?: number;
}
interface VelocityOptions {
    velocity?: number;
    restSpeed?: number;
    restDelta?: number;
}
interface DurationSpringOptions {
    duration?: number;
    visualDuration?: number;
    bounce?: number;
}
interface SpringOptions extends DurationSpringOptions, VelocityOptions {
    stiffness?: number;
    damping?: number;
    mass?: number;
}
interface DecayOptions extends VelocityOptions {
    keyframes?: number[];
    power?: number;
    timeConstant?: number;
    modifyTarget?: (v: number) => number;
}
type EasingFunction = (v: number) => number;
type EasingModifier = (easing: EasingFunction) => EasingFunction;
type BezierDefinition = readonly [number, number, number, number];
type EasingDefinition = BezierDefinition | "linear" | "easeIn" | "easeOut" | "easeInOut" | "circIn" | "circOut" | "circInOut" | "backIn" | "backOut" | "backInOut" | "anticipate";
/**
 * The easing function to use. Set as one of:
 *
 * - The name of an in-built easing function.
 * - An array of four numbers to define a cubic bezier curve.
 * - An easing function, that accepts and returns a progress value between `0` and `1`.
 *
 * @public
 */
type Easing = EasingDefinition | EasingFunction;
interface DriverControls {
    start: () => void;
    stop: () => void;
    now: () => number;
}
type Driver = (update: (timestamp: number) => void) => DriverControls;
interface InertiaOptions extends DecayOptions {
    bounceStiffness?: number;
    bounceDamping?: number;
    min?: number;
    max?: number;
}
interface KeyframeOptions {
    ease?: Easing | Easing[];
    times?: number[];
}
interface Transition extends AnimationPlaybackOptions, Omit<SpringOptions, "keyframes">, Omit<InertiaOptions, "keyframes">, KeyframeOptions {
    delay?: number;
    elapsed?: number;
    driver?: Driver;
    type?: AnimationGeneratorType;
    duration?: number;
    autoplay?: boolean;
    startTime?: number;
}
type SVGPathTransitions = {
    [K in keyof SVGPathProperties]: Transition;
};
type SVGTransitions = {
    [K in keyof SVGAttributes]: Transition;
};
type VariableTransitions = {
    [key: `--${string}`]: Transition;
};
type StyleTransitions = {
    [K in keyof CSSStyleDeclarationWithTransform]?: Transition;
};
type ValueKeyframe = string | number;
type UnresolvedValueKeyframe = ValueKeyframe | null;
type ResolvedValueKeyframe = ValueKeyframe | ValueKeyframe[];
type ValueKeyframesDefinition = ValueKeyframe | ValueKeyframe[] | UnresolvedValueKeyframe[];
type StyleKeyframesDefinition = {
    [K in keyof CSSStyleDeclarationWithTransform]?: ValueKeyframesDefinition;
};
type SVGKeyframesDefinition = {
    [K in keyof SVGAttributes]?: ValueKeyframesDefinition;
};
type VariableKeyframesDefinition = {
    [key: `--${string}`]: ValueKeyframesDefinition;
};
type SVGPathKeyframesDefinition = {
    [K in keyof SVGPathProperties]?: ValueKeyframesDefinition;
};
type DOMKeyframesDefinition = StyleKeyframesDefinition & SVGKeyframesDefinition & SVGPathKeyframesDefinition & VariableKeyframesDefinition;
interface CSSStyleDeclarationWithTransform extends Omit<CSSStyleDeclaration, "direction" | "transition" | "x" | "y" | "z"> {
    x: number | string;
    y: number | string;
    z: number | string;
    rotateX: number | string;
    rotateY: number | string;
    rotateZ: number | string;
    scaleX: number;
    scaleY: number;
    scaleZ: number;
    skewX: number | string;
    skewY: number | string;
}
type AnimationOptionsWithValueOverrides<V = any> = StyleTransitions & SVGPathTransitions & SVGTransitions & VariableTransitions & ValueAnimationTransition<V>;
type DynamicOption<T> = (i: number, total: number) => T;
interface AnimationOptions extends Omit<AnimationOptionsWithValueOverrides, "delay"> {
    delay?: number | DynamicOption<number>;
}
interface TransformProperties {
    x?: string | number;
    y?: string | number;
    z?: string | number;
    translateX?: string | number;
    translateY?: string | number;
    translateZ?: string | number;
    rotate?: string | number;
    rotateX?: string | number;
    rotateY?: string | number;
    rotateZ?: string | number;
    scale?: string | number;
    scaleX?: string | number;
    scaleY?: string | number;
    scaleZ?: string | number;
    skew?: string | number;
    skewX?: string | number;
    skewY?: string | number;
    originX?: string | number;
    originY?: string | number;
    originZ?: string | number;
    perspective?: string | number;
    transformPerspective?: string | number;
}
interface SVGPathProperties {
    pathLength?: number;
    pathOffset?: number;
    pathSpacing?: number;
}

type AcceptedAnimations = AnimationPlaybackControls | AnimationPlaybackControlsWithFinished;
type GroupedAnimations = AcceptedAnimations[];
declare class BaseGroupPlaybackControls implements AnimationPlaybackControlsWithFinished {
    animations: GroupedAnimations;
    constructor(animations: Array<AcceptedAnimations | undefined>);
    get finished(): Promise<any[]>;
    /**
     * TODO: Filter out cancelled or stopped animations before returning
     */
    private getAll;
    private setAll;
    attachTimeline(timeline: any, fallback?: (animation: AnimationPlaybackControls | AnimationPlaybackControlsWithFinished) => VoidFunction): () => void;
    get time(): number;
    set time(time: number);
    get speed(): number;
    set speed(speed: number);
    get startTime(): any;
    get duration(): number;
    private runAll;
    flatten(): void;
    play(): void;
    pause(): void;
    stop: () => void;
    cancel(): void;
    complete(): void;
}

/**
 * TODO: This is a temporary class to support the legacy
 * thennable API
 */
declare class GroupPlaybackControls extends BaseGroupPlaybackControls implements AnimationPlaybackControls {
    animations: AnimationPlaybackControls[];
    then(onResolve: VoidFunction, onReject?: VoidFunction): Promise<void>;
}

declare function getValueTransition(transition: any, key: string): any;

/**
 * Implement a practical max duration for keyframe generation
 * to prevent infinite loops
 */
declare const maxGeneratorDuration = 20000;
declare function calcGeneratorDuration(generator: KeyframeGenerator<unknown>): number;

/**
 * Create a progress => progress easing function from a generator.
 */
declare function createGeneratorEasing(options: Transition, scale: number | undefined, createGenerator: GeneratorFactory): {
    type: string;
    ease: (progress: number) => number;
    duration: number;
};

declare function isGenerator(type?: AnimationGeneratorType): type is GeneratorFactory;

declare class NativeAnimationControls implements Omit<AnimationPlaybackControlsWithFinished, "then"> {
    animation?: Animation;
    options?: ValueAnimationOptions;
    constructor(animation?: Animation);
    get duration(): number;
    get time(): number;
    set time(newTime: number);
    get speed(): number;
    set speed(newSpeed: number);
    get state(): AnimationPlayState;
    get startTime(): number | null;
    get finished(): Promise<void> | Promise<Animation>;
    play(): void;
    pause(): void;
    stop(): void;
    flatten(): void;
    attachTimeline(timeline: any): (any: void) => void;
    complete(): void;
    cancel(): void;
}

declare function attachTimeline(animation: Animation, timeline: any): void;

declare function isWaapiSupportedEasing(easing?: Easing | Easing[]): boolean;
declare const cubicBezierAsString: ([a, b, c, d]: BezierDefinition) => string;
declare const supportedWaapiEasing: {
    linear: string;
    ease: string;
    easeIn: string;
    easeOut: string;
    easeInOut: string;
    circIn: string;
    circOut: string;
    backIn: string;
    backOut: string;
};
declare function mapEasingToNativeEasing(easing: Easing | Easing[] | undefined, duration: number): undefined | string | string[];

declare const generateLinearEasing: (easing: EasingFunction, duration: number, resolution?: number) => string;

type ElementOrSelector = Element | Element[] | NodeListOf<Element> | string;
interface WithQuerySelectorAll {
    querySelectorAll: Element["querySelectorAll"];
}
interface AnimationScope<T = any> {
    readonly current: T;
    animations: any[];
}
interface SelectorCache {
    [key: string]: NodeListOf<Element>;
}
declare function resolveElements(elementOrSelector: ElementOrSelector, scope?: AnimationScope, selectorCache?: SelectorCache): Element[];

/**
 * Options for the hover gesture.
 *
 * @public
 */
interface EventOptions {
    /**
     * Use passive event listeners. Doing so allows the browser to optimize
     * scrolling performance by not allowing the use of `preventDefault()`.
     *
     * @default true
     */
    passive?: boolean;
    /**
     * Remove the event listener after the first event.
     *
     * @default false
     */
    once?: boolean;
}

/**
 * A function to be called when a hover gesture starts.
 *
 * This function can optionally return a function that will be called
 * when the hover gesture ends.
 *
 * @public
 */
type OnHoverStartEvent = (event: PointerEvent) => void | OnHoverEndEvent;
/**
 * A function to be called when a hover gesture ends.
 *
 * @public
 */
type OnHoverEndEvent = (event: PointerEvent) => void;
/**
 * Create a hover gesture. hover() is different to .addEventListener("pointerenter")
 * in that it has an easier syntax, filters out polyfilled touch events, interoperates
 * with drag gestures, and automatically removes the "pointerennd" event listener when the hover ends.
 *
 * @public
 */
declare function hover(elementOrSelector: ElementOrSelector, onHoverStart: OnHoverStartEvent, options?: EventOptions): VoidFunction;

interface PressGestureInfo {
    success: boolean;
}
type OnPressEndEvent = (event: PointerEvent, info: PressGestureInfo) => void;
type OnPressStartEvent = (event: PointerEvent) => OnPressEndEvent | void;

interface PointerEventOptions extends EventOptions {
    useGlobalTarget?: boolean;
}
/**
 * Create a press gesture.
 *
 * Press is different to `"pointerdown"`, `"pointerup"` in that it
 * automatically filters out secondary pointer events like right
 * click and multitouch.
 *
 * It also adds accessibility support for keyboards, where
 * an element with a press gesture will receive focus and
 *  trigger on Enter `"keydown"` and `"keyup"` events.
 *
 * This is different to a browser's `"click"` event, which does
 * respond to keyboards but only for the `"click"` itself, rather
 * than the press start and end/cancel. The element also needs
 * to be focusable for this to work, whereas a press gesture will
 * make an element focusable by default.
 *
 * @public
 */
declare function press(elementOrSelector: ElementOrSelector, onPressStart: OnPressStartEvent, options?: PointerEventOptions): VoidFunction;

declare const isBezierDefinition: (easing: Easing | Easing[]) => easing is BezierDefinition;

/**
 * Add the ability for test suites to manually set support flags
 * to better test more environments.
 */
declare const supportsFlags: Record<string, boolean | undefined>;

declare const supportsLinearEasing: () => boolean;

declare global {
    interface Window {
        ScrollTimeline: ScrollTimeline;
    }
}
declare class ScrollTimeline implements ProgressTimeline {
    constructor(options: ScrollOptions);
    currentTime: null | {
        value: number;
    };
    cancel?: VoidFunction;
}
declare const supportsScrollTimeline: () => boolean;

type ViewTransitionAnimationDefinition = {
    keyframes: DOMKeyframesDefinition;
    options: AnimationOptions;
};
type ViewTransitionTarget = {
    layout?: ViewTransitionAnimationDefinition;
    enter?: ViewTransitionAnimationDefinition;
    exit?: ViewTransitionAnimationDefinition;
    new?: ViewTransitionAnimationDefinition;
    old?: ViewTransitionAnimationDefinition;
};
interface ViewTransitionOptions extends AnimationOptions {
}
type Target = string | Element;

/**
 * TODO:
 * - Create view transition on next tick
 * - Replace animations with Motion animations
 * - Return GroupAnimation on next tick
 */
declare class ViewTransitionBuilder {
    private currentTarget;
    private targets;
    private notifyReady;
    private readyPromise;
    constructor(update: () => void | Promise<void>, options?: ViewTransitionOptions);
    get(selector: Target): this;
    layout(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    new(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    old(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    enter(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    exit(keyframes: DOMKeyframesDefinition, options?: AnimationOptions): this;
    crossfade(options?: AnimationOptions): this;
    updateTarget(target: "enter" | "exit" | "layout" | "new" | "old", keyframes: DOMKeyframesDefinition, options?: AnimationOptions): void;
    then(resolve: () => void, reject?: () => void): Promise<void>;
}
declare function view(update: () => void | Promise<void>, defaultOptions?: ViewTransitionOptions): ViewTransitionBuilder;

declare const isDragging: {
    x: boolean;
    y: boolean;
};
declare function isDragActive(): boolean;

declare function setDragLock(axis: boolean | "x" | "y" | "lockDirection"): (() => void) | null;

/**
 * Recursively traverse up the tree to check whether the provided child node
 * is the parent or a descendant of it.
 *
 * @param parent - Element to find
 * @param child - Element to test against parent
 */
declare const isNodeOrChild: (parent: Element | null, child?: Element | null) => boolean;

declare const isPrimaryPointer: (event: PointerEvent) => boolean;

export { type AnimationGeneratorType, type AnimationOptions, type AnimationOptionsWithValueOverrides, type AnimationPlaybackControls, type AnimationPlaybackControlsWithFinished, type AnimationPlaybackLifecycles, type AnimationPlaybackOptions, type AnimationScope, type AnimationState, type BezierDefinition, type CSSStyleDeclarationWithTransform, type DOMKeyframesDefinition, type DecayOptions, type Driver, type DriverControls, type DurationSpringOptions, type DynamicOption, type Easing, type EasingDefinition, type EasingFunction, type EasingModifier, type ElementOrSelector, type EventOptions, type GeneratorFactory, GroupPlaybackControls, type InertiaOptions, type KeyframeGenerator, type KeyframeOptions, NativeAnimationControls, type OnHoverEndEvent, type OnHoverStartEvent, type OnPressEndEvent, type OnPressStartEvent, type PointerEventOptions, type PressGestureInfo, type ProgressTimeline, type RepeatType, type ResolvedValueKeyframe, type SVGAttributes, type SVGKeyframesDefinition, type SVGPathKeyframesDefinition, type SVGPathProperties, type SVGPathTransitions, type SVGTransitions, type SelectorCache, type SpringOptions, type StyleKeyframesDefinition, type StyleTransitions, type Target, type TransformProperties, type Transition, type UnresolvedValueKeyframe, type ValueAnimationOptions, type ValueAnimationTransition, type ValueKeyframe, type ValueKeyframesDefinition, type VariableKeyframesDefinition, type VariableTransitions, type VelocityOptions, type ViewTransitionAnimationDefinition, ViewTransitionBuilder, type ViewTransitionOptions, type ViewTransitionTarget, type WithQuerySelectorAll, attachTimeline, calcGeneratorDuration, createGeneratorEasing, cubicBezierAsString, generateLinearEasing, getValueTransition, hover, isBezierDefinition, isDragActive, isDragging, isGenerator, isNodeOrChild, isPrimaryPointer, isWaapiSupportedEasing, mapEasingToNativeEasing, maxGeneratorDuration, press, resolveElements, setDragLock, supportedWaapiEasing, supportsFlags, supportsLinearEasing, supportsScrollTimeline, view };
