{"version": 3, "sources": ["../../../src/server/response-cache/utils.ts"], "sourcesContent": ["import {\n  CachedRouteK<PERSON>,\n  IncrementalCacheKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type IncrementalResponseCacheEntry,\n  type ResponseCacheEntry,\n} from './types'\n\nimport RenderResult from '../render-result'\nimport { RouteKind } from '../route-kind'\n\nexport async function fromResponseCacheEntry(\n  cacheEntry: ResponseCacheEntry\n): Promise<IncrementalResponseCacheEntry> {\n  return {\n    ...cacheEntry,\n    value:\n      cacheEntry.value?.kind === CachedRouteKind.PAGES\n        ? {\n            kind: CachedRouteKind.PAGES,\n            html: await cacheEntry.value.html.toUnchunkedString(true),\n            pageData: cacheEntry.value.pageData,\n            headers: cacheEntry.value.headers,\n            status: cacheEntry.value.status,\n          }\n        : cacheEntry.value?.kind === CachedRouteKind.APP_PAGE\n          ? {\n              kind: CachedRouteKind.APP_PAGE,\n              html: await cacheEntry.value.html.toUnchunkedString(true),\n              postponed: cacheEntry.value.postponed,\n              rscData: cacheEntry.value.rscData,\n              headers: cacheEntry.value.headers,\n              status: cacheEntry.value.status,\n              segmentData: cacheEntry.value.segmentData,\n            }\n          : cacheEntry.value,\n  }\n}\n\nexport async function toResponseCacheEntry(\n  response: IncrementalResponseCacheEntry | null\n): Promise<ResponseCacheEntry | null> {\n  if (!response) return null\n\n  return {\n    isMiss: response.isMiss,\n    isStale: response.isStale,\n    cacheControl: response.cacheControl,\n    isFallback: response.isFallback,\n    value:\n      response.value?.kind === CachedRouteKind.PAGES\n        ? ({\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(response.value.html),\n            pageData: response.value.pageData,\n            headers: response.value.headers,\n            status: response.value.status,\n          } satisfies CachedPageValue)\n        : response.value?.kind === CachedRouteKind.APP_PAGE\n          ? ({\n              kind: CachedRouteKind.APP_PAGE,\n              html: RenderResult.fromStatic(response.value.html),\n              rscData: response.value.rscData,\n              headers: response.value.headers,\n              status: response.value.status,\n              postponed: response.value.postponed,\n              segmentData: response.value.segmentData,\n            } satisfies CachedAppPageValue)\n          : response.value,\n  }\n}\n\nexport function routeKindToIncrementalCacheKind(\n  routeKind: RouteKind\n): Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH> {\n  switch (routeKind) {\n    case RouteKind.PAGES:\n      return IncrementalCacheKind.PAGES\n    case RouteKind.APP_PAGE:\n      return IncrementalCacheKind.APP_PAGE\n    case RouteKind.IMAGE:\n      return IncrementalCacheKind.IMAGE\n    case RouteKind.APP_ROUTE:\n      return IncrementalCacheKind.APP_ROUTE\n    default:\n      throw new Error(`Unexpected route kind ${routeKind}`)\n  }\n}\n"], "names": ["fromResponseCacheEntry", "routeKindToIncrementalCacheKind", "toResponseCacheEntry", "cacheEntry", "value", "kind", "CachedRouteKind", "PAGES", "html", "toUnchunkedString", "pageData", "headers", "status", "APP_PAGE", "postponed", "rscData", "segmentData", "response", "isMiss", "isStale", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "RenderResult", "fromStatic", "routeKind", "RouteKind", "IncrementalCacheKind", "IMAGE", "APP_ROUTE", "Error"], "mappings": ";;;;;;;;;;;;;;;;IAYsBA,sBAAsB;eAAtBA;;IA6DNC,+BAA+B;eAA/BA;;IAjCMC,oBAAoB;eAApBA;;;uBAjCf;qEAEkB;2BACC;;;;;;AAEnB,eAAeF,uBACpBG,UAA8B;QAK1BA,mBAQIA;IAXR,OAAO;QACL,GAAGA,UAAU;QACbC,OACED,EAAAA,oBAAAA,WAAWC,KAAK,qBAAhBD,kBAAkBE,IAAI,MAAKC,sBAAe,CAACC,KAAK,GAC5C;YACEF,MAAMC,sBAAe,CAACC,KAAK;YAC3BC,MAAM,MAAML,WAAWC,KAAK,CAACI,IAAI,CAACC,iBAAiB,CAAC;YACpDC,UAAUP,WAAWC,KAAK,CAACM,QAAQ;YACnCC,SAASR,WAAWC,KAAK,CAACO,OAAO;YACjCC,QAAQT,WAAWC,KAAK,CAACQ,MAAM;QACjC,IACAT,EAAAA,qBAAAA,WAAWC,KAAK,qBAAhBD,mBAAkBE,IAAI,MAAKC,sBAAe,CAACO,QAAQ,GACjD;YACER,MAAMC,sBAAe,CAACO,QAAQ;YAC9BL,MAAM,MAAML,WAAWC,KAAK,CAACI,IAAI,CAACC,iBAAiB,CAAC;YACpDK,WAAWX,WAAWC,KAAK,CAACU,SAAS;YACrCC,SAASZ,WAAWC,KAAK,CAACW,OAAO;YACjCJ,SAASR,WAAWC,KAAK,CAACO,OAAO;YACjCC,QAAQT,WAAWC,KAAK,CAACQ,MAAM;YAC/BI,aAAab,WAAWC,KAAK,CAACY,WAAW;QAC3C,IACAb,WAAWC,KAAK;IAC1B;AACF;AAEO,eAAeF,qBACpBe,QAA8C;QAU1CA,iBAQIA;IAhBR,IAAI,CAACA,UAAU,OAAO;IAEtB,OAAO;QACLC,QAAQD,SAASC,MAAM;QACvBC,SAASF,SAASE,OAAO;QACzBC,cAAcH,SAASG,YAAY;QACnCC,YAAYJ,SAASI,UAAU;QAC/BjB,OACEa,EAAAA,kBAAAA,SAASb,KAAK,qBAAda,gBAAgBZ,IAAI,MAAKC,sBAAe,CAACC,KAAK,GACzC;YACCF,MAAMC,sBAAe,CAACC,KAAK;YAC3BC,MAAMc,qBAAY,CAACC,UAAU,CAACN,SAASb,KAAK,CAACI,IAAI;YACjDE,UAAUO,SAASb,KAAK,CAACM,QAAQ;YACjCC,SAASM,SAASb,KAAK,CAACO,OAAO;YAC/BC,QAAQK,SAASb,KAAK,CAACQ,MAAM;QAC/B,IACAK,EAAAA,mBAAAA,SAASb,KAAK,qBAAda,iBAAgBZ,IAAI,MAAKC,sBAAe,CAACO,QAAQ,GAC9C;YACCR,MAAMC,sBAAe,CAACO,QAAQ;YAC9BL,MAAMc,qBAAY,CAACC,UAAU,CAACN,SAASb,KAAK,CAACI,IAAI;YACjDO,SAASE,SAASb,KAAK,CAACW,OAAO;YAC/BJ,SAASM,SAASb,KAAK,CAACO,OAAO;YAC/BC,QAAQK,SAASb,KAAK,CAACQ,MAAM;YAC7BE,WAAWG,SAASb,KAAK,CAACU,SAAS;YACnCE,aAAaC,SAASb,KAAK,CAACY,WAAW;QACzC,IACAC,SAASb,KAAK;IACxB;AACF;AAEO,SAASH,gCACduB,SAAoB;IAEpB,OAAQA;QACN,KAAKC,oBAAS,CAAClB,KAAK;YAClB,OAAOmB,2BAAoB,CAACnB,KAAK;QACnC,KAAKkB,oBAAS,CAACZ,QAAQ;YACrB,OAAOa,2BAAoB,CAACb,QAAQ;QACtC,KAAKY,oBAAS,CAACE,KAAK;YAClB,OAAOD,2BAAoB,CAACC,KAAK;QACnC,KAAKF,oBAAS,CAACG,SAAS;YACtB,OAAOF,2BAAoB,CAACE,SAAS;QACvC;YACE,MAAM,qBAA+C,CAA/C,IAAIC,MAAM,CAAC,sBAAsB,EAAEL,WAAW,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF"}