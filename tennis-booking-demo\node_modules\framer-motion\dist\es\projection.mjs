export { HTMLProjectionNode } from './projection/node/HTMLProjectionNode.mjs';
export { nodeGroup } from './projection/node/group.mjs';
export { calcBoxDelta } from './projection/geometry/delta-calc.mjs';
export { mix } from './utils/mix/index.mjs';
export { animateValue as animate } from './animation/animators/MainThreadAnimation.mjs';
export { buildTransform } from './render/html/utils/build-transform.mjs';
export { addScaleCorrector } from './projection/styles/scale-correction.mjs';
export { correctBorderRadius } from './projection/styles/scale-border-radius.mjs';
export { correctBoxShadow } from './projection/styles/scale-box-shadow.mjs';
export { HTMLVisualElement } from './render/html/HTMLVisualElement.mjs';
export { frame, frameData } from './frameloop/frame.mjs';
