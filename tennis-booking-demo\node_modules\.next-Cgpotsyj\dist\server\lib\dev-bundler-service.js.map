{"version": 3, "sources": ["../../../src/server/lib/dev-bundler-service.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { <PERSON>B<PERSON>ler } from './router-utils/setup-dev-bundler'\nimport type { WorkerRequestHandler } from './types'\n\nimport { LRUCache } from './lru-cache'\nimport { createRequestResponseMocks } from './mock-request'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../dev/hot-reloader-types'\n\n/**\n * The DevBundlerService provides an interface to perform tasks with the\n * bundler while in development.\n */\nexport class DevBundlerService {\n  public appIsrManifestInner: InstanceType<typeof LRUCache>\n\n  constructor(\n    private readonly bundler: DevBundler,\n    private readonly handler: WorkerRequestHandler\n  ) {\n    this.appIsrManifestInner = new LRUCache(\n      8_000,\n\n      function length() {\n        return 16\n      }\n    ) as any\n  }\n\n  public ensurePage: typeof this.bundler.hotReloader.ensurePage = async (\n    definition\n  ) => {\n    // TODO: remove after ensure is pulled out of server\n    return await this.bundler.hotReloader.ensurePage(definition)\n  }\n\n  public logErrorWithOriginalStack =\n    this.bundler.logErrorWithOriginalStack.bind(this.bundler)\n\n  public async getFallbackErrorComponents(url?: string) {\n    await this.bundler.hotReloader.buildFallbackError()\n    // Build the error page to ensure the fallback is built too.\n    // TODO: See if this can be moved into hotReloader or removed.\n    await this.bundler.hotReloader.ensurePage({\n      page: '/_error',\n      clientOnly: false,\n      definition: undefined,\n      url,\n    })\n  }\n\n  public async getCompilationError(page: string) {\n    const errors = await this.bundler.hotReloader.getCompilationErrors(page)\n    if (!errors) return\n\n    // Return the very first error we found.\n    return errors[0]\n  }\n\n  public async revalidate({\n    urlPath,\n    revalidateHeaders,\n    opts: revalidateOpts,\n  }: {\n    urlPath: string\n    revalidateHeaders: IncomingMessage['headers']\n    opts: any\n  }) {\n    const mocked = createRequestResponseMocks({\n      url: urlPath,\n      headers: revalidateHeaders,\n    })\n\n    await this.handler(mocked.req, mocked.res)\n    await mocked.res.hasStreamed\n\n    if (\n      mocked.res.getHeader('x-nextjs-cache') !== 'REVALIDATED' &&\n      mocked.res.statusCode !== 200 &&\n      !(mocked.res.statusCode === 404 && revalidateOpts.unstable_onlyGenerated)\n    ) {\n      throw new Error(`Invalid response ${mocked.res.statusCode}`)\n    }\n\n    return {}\n  }\n\n  public get appIsrManifest() {\n    const serializableManifest: Record<string, boolean> = {}\n\n    for (const key of this.appIsrManifestInner.keys() as string[]) {\n      serializableManifest[key] = this.appIsrManifestInner.get(key) as boolean\n    }\n    return serializableManifest\n  }\n\n  public setIsrStatus(key: string, value: boolean | null) {\n    if (value === null) {\n      this.appIsrManifestInner.remove(key)\n    } else {\n      this.appIsrManifestInner.set(key, value)\n    }\n    this.bundler?.hotReloader?.send({\n      action: HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST,\n      data: this.appIsrManifest,\n    })\n  }\n\n  public close() {\n    this.bundler.hotReloader.close()\n  }\n}\n"], "names": ["DevBundlerService", "constructor", "bundler", "handler", "ensurePage", "definition", "hotReloader", "logErrorWithOriginalStack", "bind", "appIsrManifestInner", "L<PERSON><PERSON><PERSON>", "length", "getFallbackErrorComponents", "url", "buildFallbackError", "page", "clientOnly", "undefined", "getCompilationError", "errors", "getCompilationErrors", "revalidate", "url<PERSON><PERSON>", "revalidateHeaders", "opts", "revalidateOpts", "mocked", "createRequestResponseMocks", "headers", "req", "res", "hasStreamed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "unstable_onlyGenerated", "Error", "appIsrManifest", "serializableManifest", "key", "keys", "get", "setIsrStatus", "value", "remove", "set", "send", "action", "HMR_ACTIONS_SENT_TO_BROWSER", "ISR_MANIFEST", "data", "close"], "mappings": ";;;;+BAYaA;;;eAAAA;;;0BARY;6BACkB;kCACC;AAMrC,MAAMA;IAGXC,YACE,AAAiBC,OAAmB,EACpC,AAAiBC,OAA6B,CAC9C;aAFiBD,UAAAA;aACAC,UAAAA;aAWZC,aAAyD,OAC9DC;YAEA,oDAAoD;YACpD,OAAO,MAAM,IAAI,CAACH,OAAO,CAACI,WAAW,CAACF,UAAU,CAACC;QACnD;aAEOE,4BACL,IAAI,CAACL,OAAO,CAACK,yBAAyB,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO;QAjBxD,IAAI,CAACO,mBAAmB,GAAG,IAAIC,kBAAQ,CACrC,MAEA,SAASC;YACP,OAAO;QACT;IAEJ;IAYA,MAAaC,2BAA2BC,GAAY,EAAE;QACpD,MAAM,IAAI,CAACX,OAAO,CAACI,WAAW,CAACQ,kBAAkB;QACjD,4DAA4D;QAC5D,8DAA8D;QAC9D,MAAM,IAAI,CAACZ,OAAO,CAACI,WAAW,CAACF,UAAU,CAAC;YACxCW,MAAM;YACNC,YAAY;YACZX,YAAYY;YACZJ;QACF;IACF;IAEA,MAAaK,oBAAoBH,IAAY,EAAE;QAC7C,MAAMI,SAAS,MAAM,IAAI,CAACjB,OAAO,CAACI,WAAW,CAACc,oBAAoB,CAACL;QACnE,IAAI,CAACI,QAAQ;QAEb,wCAAwC;QACxC,OAAOA,MAAM,CAAC,EAAE;IAClB;IAEA,MAAaE,WAAW,EACtBC,OAAO,EACPC,iBAAiB,EACjBC,MAAMC,cAAc,EAKrB,EAAE;QACD,MAAMC,SAASC,IAAAA,uCAA0B,EAAC;YACxCd,KAAKS;YACLM,SAASL;QACX;QAEA,MAAM,IAAI,CAACpB,OAAO,CAACuB,OAAOG,GAAG,EAAEH,OAAOI,GAAG;QACzC,MAAMJ,OAAOI,GAAG,CAACC,WAAW;QAE5B,IACEL,OAAOI,GAAG,CAACE,SAAS,CAAC,sBAAsB,iBAC3CN,OAAOI,GAAG,CAACG,UAAU,KAAK,OAC1B,CAAEP,CAAAA,OAAOI,GAAG,CAACG,UAAU,KAAK,OAAOR,eAAeS,sBAAsB,AAAD,GACvE;YACA,MAAM,qBAAsD,CAAtD,IAAIC,MAAM,CAAC,iBAAiB,EAAET,OAAOI,GAAG,CAACG,UAAU,EAAE,GAArD,qBAAA;uBAAA;4BAAA;8BAAA;YAAqD;QAC7D;QAEA,OAAO,CAAC;IACV;IAEA,IAAWG,iBAAiB;QAC1B,MAAMC,uBAAgD,CAAC;QAEvD,KAAK,MAAMC,OAAO,IAAI,CAAC7B,mBAAmB,CAAC8B,IAAI,GAAgB;YAC7DF,oBAAoB,CAACC,IAAI,GAAG,IAAI,CAAC7B,mBAAmB,CAAC+B,GAAG,CAACF;QAC3D;QACA,OAAOD;IACT;IAEOI,aAAaH,GAAW,EAAEI,KAAqB,EAAE;YAMtD,2BAAA;QALA,IAAIA,UAAU,MAAM;YAClB,IAAI,CAACjC,mBAAmB,CAACkC,MAAM,CAACL;QAClC,OAAO;YACL,IAAI,CAAC7B,mBAAmB,CAACmC,GAAG,CAACN,KAAKI;QACpC;SACA,gBAAA,IAAI,CAACxC,OAAO,sBAAZ,4BAAA,cAAcI,WAAW,qBAAzB,0BAA2BuC,IAAI,CAAC;YAC9BC,QAAQC,6CAA2B,CAACC,YAAY;YAChDC,MAAM,IAAI,CAACb,cAAc;QAC3B;IACF;IAEOc,QAAQ;QACb,IAAI,CAAChD,OAAO,CAACI,WAAW,CAAC4C,KAAK;IAChC;AACF"}