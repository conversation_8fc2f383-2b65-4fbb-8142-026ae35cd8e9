import { Navigation } from '@/components/layout/Navigation'
import { Hero } from '@/components/landing/Hero'
import { ProblemSection } from '@/components/landing/ProblemSection'

export default function Home() {
  return (
    <main className="min-h-screen">
      <Navigation />
      <Hero />
      <ProblemSection />
      
      {/* Temporary - More sections coming soon */}
      <section className="py-20 bg-slate-100 text-center">
        <div className="max-w-4xl mx-auto px-4">
          <h2 className="text-3xl font-bold text-slate-900 mb-4">
            🚧 More Premium Sections Coming Soon! 🚧
          </h2>
          <p className="text-lg text-slate-600">
            We're building the solution showcase, interactive demo, 
            pricing shock reveal, and more premium sections with stunning animations.
          </p>
          <div className="mt-8 p-6 bg-white rounded-xl shadow-lg">
            <p className="text-sm text-slate-500">
              Preview: Hero section with premium animations ✅<br/>
              Preview: Problem section with scroll reveals ✅<br/>
              Next: Solution showcase with phone mockup animations<br/>
              Next: Pricing section with the €39 shock reveal<br/>
              Next: Interactive demo and social proof sections
            </p>
          </div>
        </div>
      </section>
    </main>
  )
}
