{"version": 3, "sources": ["../../../src/server/request/connection.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  postponeWithTracking,\n  throwToInterruptStaticGeneration,\n  trackDynamicDataInDynamicRender,\n} from '../app-render/dynamic-rendering'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { isRequestAPICallableInsideAfter } from './utils'\n\n/**\n * This function allows you to indicate that you require an actual user Request before continuing.\n *\n * During prerendering it will never resolve and during rendering it resolves immediately.\n */\nexport function connection(): Promise<void> {\n  const workStore = workAsyncStorage.getStore()\n  const workUnitStore = workUnitAsyncStorage.getStore()\n\n  if (workStore) {\n    if (\n      workUnitStore &&\n      workUnitStore.phase === 'after' &&\n      !isRequestAPICallableInsideAfter()\n    ) {\n      throw new Error(\n        `Route ${workStore.route} used \"connection\" inside \"after(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but \"after(...)\" executes after the request, so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`\n      )\n    }\n\n    if (workStore.forceStatic) {\n      // When using forceStatic we override all other logic and always just return an empty\n      // headers object without tracking\n      return Promise.resolve(undefined)\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"connection\" inside \"use cache\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n        )\n      } else if (workUnitStore.type === 'unstable-cache') {\n        throw new Error(\n          `Route ${workStore.route} used \"connection\" inside a function cached with \"unstable_cache(...)\". The \\`connection()\\` function is used to indicate the subsequent code must only run when there is an actual Request, but caches must be able to be produced before a Request so this function is not allowed in this scope. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n        )\n      }\n    }\n    if (workStore.dynamicShouldError) {\n      throw new StaticGenBailoutError(\n        `Route ${workStore.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`connection\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n      )\n    }\n\n    if (workUnitStore) {\n      if (workUnitStore.type === 'prerender') {\n        // dynamicIO Prerender\n        // We return a promise that never resolves to allow the prender to stall at this point\n        return makeHangingPromise(workUnitStore.renderSignal, '`connection()`')\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // PPR Prerender (no dynamicIO)\n        // We use React's postpone API to interrupt rendering here to create a dynamic hole\n        postponeWithTracking(\n          workStore.route,\n          'connection',\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        // Legacy Prerender\n        // We throw an error here to interrupt prerendering to mark the route as dynamic\n        throwToInterruptStaticGeneration('connection', workStore, workUnitStore)\n      }\n    }\n    // We fall through to the dynamic context below but we still track dynamic access\n    // because in dev we can still error for things like using headers inside a cache context\n    trackDynamicDataInDynamicRender(workStore, workUnitStore)\n  }\n\n  return Promise.resolve(undefined)\n}\n"], "names": ["connection", "workStore", "workAsyncStorage", "getStore", "workUnitStore", "workUnitAsyncStorage", "phase", "isRequestAPICallableInsideAfter", "Error", "route", "forceStatic", "Promise", "resolve", "undefined", "type", "dynamicShouldError", "StaticGenBailoutError", "makeHangingPromise", "renderSignal", "postponeWithTracking", "dynamicTracking", "throwToInterruptStaticGeneration", "trackDynamicDataInDynamicRender"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;0CAhBiB;8CACI;kCAK9B;yCAC+B;uCACH;uBACa;AAOzC,SAASA;IACd,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAC3C,MAAMC,gBAAgBC,kDAAoB,CAACF,QAAQ;IAEnD,IAAIF,WAAW;QACb,IACEG,iBACAA,cAAcE,KAAK,KAAK,WACxB,CAACC,IAAAA,sCAA+B,KAChC;YACA,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,6UAA6U,CAAC,GADnW,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIR,UAAUS,WAAW,EAAE;YACzB,qFAAqF;YACrF,kCAAkC;YAClC,OAAOC,QAAQC,OAAO,CAACC;QACzB;QAEA,IAAIT,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,SAAS;gBAClC,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,iVAAiV,CAAC,GADvW,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF,OAAO,IAAIL,cAAcU,IAAI,KAAK,kBAAkB;gBAClD,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,MAAM,EAAEP,UAAUQ,KAAK,CAAC,0XAA0X,CAAC,GADhZ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QACA,IAAIR,UAAUc,kBAAkB,EAAE;YAChC,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,MAAM,EAAEf,UAAUQ,KAAK,CAAC,oNAAoN,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIL,eAAe;YACjB,IAAIA,cAAcU,IAAI,KAAK,aAAa;gBACtC,sBAAsB;gBACtB,sFAAsF;gBACtF,OAAOG,IAAAA,yCAAkB,EAACb,cAAcc,YAAY,EAAE;YACxD,OAAO,IAAId,cAAcU,IAAI,KAAK,iBAAiB;gBACjD,+BAA+B;gBAC/B,mFAAmF;gBACnFK,IAAAA,sCAAoB,EAClBlB,UAAUQ,KAAK,EACf,cACAL,cAAcgB,eAAe;YAEjC,OAAO,IAAIhB,cAAcU,IAAI,KAAK,oBAAoB;gBACpD,mBAAmB;gBACnB,gFAAgF;gBAChFO,IAAAA,kDAAgC,EAAC,cAAcpB,WAAWG;YAC5D;QACF;QACA,iFAAiF;QACjF,yFAAyF;QACzFkB,IAAAA,iDAA+B,EAACrB,WAAWG;IAC7C;IAEA,OAAOO,QAAQC,OAAO,CAACC;AACzB"}