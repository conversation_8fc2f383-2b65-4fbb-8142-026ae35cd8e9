{"version": 3, "sources": ["../../../../../src/server/route-modules/app-route/helpers/get-pathname-from-absolute-path.ts"], "sourcesContent": ["/**\n * Get pathname from absolute path.\n *\n * @param absolutePath the absolute path\n * @returns the pathname\n */\nexport function getPathnameFromAbsolutePath(absolutePath: string) {\n  // Remove prefix including app dir\n  let appDir = '/app/'\n  if (!absolutePath.includes(appDir)) {\n    appDir = '\\\\app\\\\'\n  }\n  const [, ...parts] = absolutePath.split(appDir)\n  const relativePath = appDir[0] + parts.join(appDir)\n\n  // remove extension\n  const pathname = relativePath.split('.').slice(0, -1).join('.')\n  return pathname\n}\n"], "names": ["getPathnameFromAbsolutePath", "absolutePath", "appDir", "includes", "parts", "split", "relativePath", "join", "pathname", "slice"], "mappings": "AAAA;;;;;CAKC;;;;+BACeA;;;eAAAA;;;AAAT,SAASA,4BAA4BC,YAAoB;IAC9D,kCAAkC;IAClC,IAAIC,SAAS;IACb,IAAI,CAACD,aAAaE,QAAQ,CAACD,SAAS;QAClCA,SAAS;IACX;IACA,MAAM,GAAG,GAAGE,MAAM,GAAGH,aAAaI,KAAK,CAACH;IACxC,MAAMI,eAAeJ,MAAM,CAAC,EAAE,GAAGE,MAAMG,IAAI,CAACL;IAE5C,mBAAmB;IACnB,MAAMM,WAAWF,aAAaD,KAAK,CAAC,KAAKI,KAAK,CAAC,GAAG,CAAC,GAAGF,IAAI,CAAC;IAC3D,OAAOC;AACT"}