@import "tailwindcss";

:root {
  /* Tennis Booking Pro Brand Colors */
  --tennis-yellow: #FFD700;
  --tennis-blue: #1E40AF;
  --tennis-green: #10B981;
  --tennis-dark: #0F172A;
  --tennis-light: #F8FAFC;
  
  /* Gradient Backgrounds */
  --gradient-hero: linear-gradient(135deg, #1E40AF 0%, #3B82F6 50%, #10B981 100%);
  --gradient-cta: linear-gradient(45deg, #FFD700 0%, #F59E0B 100%);
  
  /* Glass Effect Variables */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-blur: blur(10px);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: var(--font-inter), 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Premium Glassmorphism Classes */
.glass-nav {
  backdrop-filter: var(--glass-blur);
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}

.glass-card {
  backdrop-filter: var(--glass-blur);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Premium Button Styles */
.btn-premium {
  background: var(--gradient-cta);
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-premium:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(255, 215, 0, 0.4);
}

/* Scroll Animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-up.animate {
  opacity: 1;
  transform: translateY(0);
}

/* Tennis Ball Animation */
@keyframes tennis-bounce {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.tennis-ball {
  animation: tennis-bounce 2s ease-in-out infinite;
}

/* Premium Text Gradients */
.text-gradient {
  background: var(--gradient-hero);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: var(--tennis-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1e3a8a;
}
