{"version": 3, "sources": ["../../../../src/server/app-render/metadata-insertion/create-server-inserted-metadata.tsx"], "sourcesContent": ["import React from 'react'\nimport { renderToReadableStream } from 'react-dom/server.edge'\nimport {\n  ServerInsertedMetadataContext,\n  type MetadataResolver,\n} from '../../../shared/lib/server-inserted-metadata.shared-runtime'\nimport { renderToString } from '../render-to-string'\n\n/**\n * For chromium based browsers (Chrome, Edge, etc.) and Safari,\n * icons need to stay under <head> to be picked up by the browser.\n *\n */\nconst REINSERT_ICON_SCRIPT = `\\\ndocument.querySelectorAll('body link[rel=\"icon\"], body link[rel=\"apple-touch-icon\"]').forEach(el => document.head.appendChild(el))`\n\nexport function createServerInsertedMetadata(nonce: string | undefined) {\n  let metadataResolver: MetadataResolver | null = null\n  let metadataToFlush: React.ReactNode = null\n  const setMetadataResolver = (resolver: MetadataResolver): void => {\n    metadataResolver = resolver\n  }\n\n  return {\n    ServerInsertedMetadataProvider: ({\n      children,\n    }: {\n      children: React.ReactNode\n    }) => {\n      return (\n        <ServerInsertedMetadataContext.Provider value={setMetadataResolver}>\n          {children}\n        </ServerInsertedMetadataContext.Provider>\n      )\n    },\n\n    async getServerInsertedMetadata(): Promise<string> {\n      if (!metadataResolver || metadataToFlush) {\n        return ''\n      }\n\n      metadataToFlush = metadataResolver()\n      const html = await renderToString({\n        renderToReadableStream,\n        element: (\n          <>\n            {metadataToFlush}\n            <script nonce={nonce}>{REINSERT_ICON_SCRIPT}</script>\n          </>\n        ),\n      })\n\n      return html\n    },\n  }\n}\n"], "names": ["createServerInsertedMetadata", "REINSERT_ICON_SCRIPT", "nonce", "metadataResolver", "metadataToFlush", "setMetadataResolver", "resolver", "ServerInsertedMetadataProvider", "children", "ServerInsertedMetadataContext", "Provider", "value", "getServerInsertedMetadata", "html", "renderToString", "renderToReadableStream", "element", "script"], "mappings": ";;;;+BAgBgBA;;;eAAAA;;;;8DAhBE;4BACqB;qDAIhC;gCACwB;;;;;;AAE/B;;;;CAIC,GACD,MAAMC,uBAAuB,CAAC;kIACoG,CAAC;AAE5H,SAASD,6BAA6BE,KAAyB;IACpE,IAAIC,mBAA4C;IAChD,IAAIC,kBAAmC;IACvC,MAAMC,sBAAsB,CAACC;QAC3BH,mBAAmBG;IACrB;IAEA,OAAO;QACLC,gCAAgC,CAAC,EAC/BC,QAAQ,EAGT;YACC,qBACE,qBAACC,kEAA6B,CAACC,QAAQ;gBAACC,OAAON;0BAC5CG;;QAGP;QAEA,MAAMI;YACJ,IAAI,CAACT,oBAAoBC,iBAAiB;gBACxC,OAAO;YACT;YAEAA,kBAAkBD;YAClB,MAAMU,OAAO,MAAMC,IAAAA,8BAAc,EAAC;gBAChCC,wBAAAA,kCAAsB;gBACtBC,uBACE;;wBACGZ;sCACD,qBAACa;4BAAOf,OAAOA;sCAAQD;;;;YAG7B;YAEA,OAAOY;QACT;IACF;AACF"}