!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Motion={},t.React)}(this,(function(t,e){"use strict";function i(t){const i=e.useRef(null);return null===i.current&&(i.current=t()),i.current}const n=t=>t;let a=n;function s(t){let e;return()=>(void 0===e&&(e=t()),e)}const r=(t,e,i)=>{const n=e-t;return 0===n?1:(i-t)/n},o=t=>1e3*t,l=t=>t/1e3,u=s(()=>void 0!==window.ScrollTimeline);class h extends class{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){const i=this.animations.map(i=>u()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}const c={linearEasing:void 0};function m(t,e){const i=s(t);return()=>{var t;return null!==(t=c[e])&&void 0!==t?t:i()}}const d=m(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),p=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,f={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:p([0,.65,.55,1]),circOut:p([.55,0,1,.45]),backIn:p([.31,.01,.66,-.59]),backOut:p([.33,1.53,.69,.99])};function y(t,e){return t?"function"==typeof t&&d()?((t,e,i=10)=>{let n="";const a=Math.max(Math.round(e/i),2);for(let e=0;e<a;e++)n+=t(r(0,a-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`})(t,e):(t=>Array.isArray(t)&&"number"==typeof t[0])(t)?p(t):Array.isArray(t)?t.map(t=>y(t,e)||f.easeOut):f[t]:void 0}const g=(t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}))("px"),v={borderWidth:g,borderTopWidth:g,borderRightWidth:g,borderBottomWidth:g,borderLeftWidth:g,borderRadius:g,radius:g,borderTopLeftRadius:g,borderTopRightRadius:g,borderBottomRightRadius:g,borderBottomLeftRadius:g,width:g,maxWidth:g,height:g,maxHeight:g,top:g,right:g,bottom:g,left:g,padding:g,paddingTop:g,paddingRight:g,paddingBottom:g,paddingLeft:g,margin:g,marginTop:g,marginRight:g,marginBottom:g,marginLeft:g,backgroundPositionX:g,backgroundPositionY:g},A=t=>null!==t;function b(t,e,i){t.style.setProperty("--"+e,i)}function T(t,e,i){t.style[e]=i}const P=s(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(t){return!1}return!0}),R=s(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),x=new WeakMap;function w(t){const e=x.get(t)||new Map;return x.set(t,e),x.get(t)}class k extends class{constructor(t){this.animation=t}get duration(){var t,e,i;const n=(null===(e=null===(t=this.animation)||void 0===t?void 0:t.effect)||void 0===e?void 0:e.getComputedTiming().duration)||(null===(i=this.options)||void 0===i?void 0:i.duration)||300;return l(Number(n))}get time(){var t;return this.animation?l((null===(t=this.animation)||void 0===t?void 0:t.currentTime)||0):0}set time(t){this.animation&&(this.animation.currentTime=o(t))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(t){this.animation&&(this.animation.playbackRate=t)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}get finished(){return this.animation?this.animation.finished:Promise.resolve()}play(){this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}flatten(){var t;this.animation&&(null===(t=this.animation.effect)||void 0===t||t.updateTiming({easing:"linear"}))}attachTimeline(t){return this.animation&&function(t,e){t.timeline=e,t.onfinish=null}(this.animation,t),n}complete(){this.animation&&this.animation.finish()}cancel(){try{this.animation&&this.animation.cancel()}catch(t){}}}{constructor(t,e,i,n){const s=e.startsWith("--");a("string"!=typeof n.type);const r=w(t).get(e);r&&r.stop();if(Array.isArray(i)||(i=[i]),function(t,e,i){for(let n=0;n<e.length;n++)null===e[n]&&(e[n]=0===n?i():e[n-1]),"number"==typeof e[n]&&v[t]&&(e[n]=v[t].transform(e[n]));!P()&&e.length<2&&e.unshift(i())}(e,i,()=>e.startsWith("--")?t.style.getPropertyValue(e):window.getComputedStyle(t)[e]),"function"==typeof n.type){const t=function(t,e=100,i){const n=i({...t,keyframes:[0,e]}),a=Math.min(function(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}(n),2e4);return{type:"keyframes",ease:t=>n.next(a*t).value/e,duration:l(a)}}(n,100,n.type);n.ease=d()?t.ease:"easeOut",n.duration=o(t.duration),n.type="keyframes"}else n.ease=n.ease||"easeOut";const u=()=>{this.setValue(t,e,function(t,{repeat:e,repeatType:i="loop"},n){const a=t.filter(A),s=e&&"loop"!==i&&e%2==1?0:a.length-1;return s&&void 0!==n?n:a[s]}(i,n)),this.cancel(),this.resolveFinishedPromise()},h=()=>{this.setValue=s?b:T,this.options=n,this.updateFinishedPromise(),this.removeAnimation=()=>{const i=x.get(t);i&&i.delete(e)}};R()?(super(function(t,e,i,{delay:n=0,duration:a=300,repeat:s=0,repeatType:r="loop",ease:o="easeInOut",times:l}={}){const u={[e]:i};l&&(u.offset=l);const h=y(o,a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:a,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===r?"alternate":"normal"})}(t,e,i,n)),h(),!1===n.autoplay&&this.animation.pause(),this.animation.onfinish=u,w(t).set(e,this)):(super(),h(),u())}then(t,e){return this.currentFinishedPromise.then(t,e)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}play(){"finished"===this.state&&this.updateFinishedPromise(),super.play()}cancel(){this.removeAnimation(),super.cancel()}}function E(t,e,i,n){const a=function(t,e,i){var n;if(t instanceof Element)return[t];if("string"==typeof t){let a=document;e&&(a=e.current);const s=null!==(n=null==i?void 0:i[t])&&void 0!==n?n:a.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}(t,n),s=a.length,r=[];for(let t=0;t<s;t++){const n=a[t],h={...i};"function"==typeof h.delay&&(h.delay=h.delay(t,s));for(const t in e){const i=e[t],a={...(l=h,u=t,l?l[u]||l.default||l:void 0)};a.duration=a.duration?o(a.duration):a.duration,a.delay=o(a.delay||0),r.push(new k(n,t,i,a))}}var l,u;return r}t.useAnimate=function(){const t=i(()=>({current:null,animations:[]})),n=i(()=>(t=>function(e,i,n){return new h(E(e,i,n,t))})(t));var a;return a=()=>{t.animations.forEach(t=>t.stop())},e.useEffect(()=>()=>a(),[]),[t,n]}}));
