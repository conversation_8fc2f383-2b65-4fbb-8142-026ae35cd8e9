{"version": 3, "sources": ["../../../src/server/use-cache/cache-tag.ts"], "sourcesContent": ["import { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { validateTags } from '../lib/patch-fetch'\n\nexport function cacheTag(...tags: string[]): void {\n  if (!process.env.__NEXT_USE_CACHE) {\n    throw new Error(\n      'cacheTag() is only available with the experimental.useCache config.'\n    )\n  }\n\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (!workUnitStore || workUnitStore.type !== 'cache') {\n    throw new Error(\n      'cacheTag() can only be called inside a \"use cache\" function.'\n    )\n  }\n\n  const validTags = validateTags(tags, 'cacheTag()')\n\n  if (!workUnitStore.tags) {\n    workUnitStore.tags = validTags\n  } else {\n    workUnitStore.tags.push(...validTags)\n  }\n}\n"], "names": ["cacheTag", "tags", "process", "env", "__NEXT_USE_CACHE", "Error", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "validTags", "validateTags", "push"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;8CAHqB;4BACR;AAEtB,SAASA,SAAS,GAAGC,IAAc;IACxC,IAAI,CAACC,QAAQC,GAAG,CAACC,gBAAgB,EAAE;QACjC,MAAM,qBAEL,CAFK,IAAIC,MACR,wEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,gBAAgBC,kDAAoB,CAACC,QAAQ;IACnD,IAAI,CAACF,iBAAiBA,cAAcG,IAAI,KAAK,SAAS;QACpD,MAAM,qBAEL,CAFK,IAAIJ,MACR,iEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMK,YAAYC,IAAAA,wBAAY,EAACV,MAAM;IAErC,IAAI,CAACK,cAAcL,IAAI,EAAE;QACvBK,cAAcL,IAAI,GAAGS;IACvB,OAAO;QACLJ,cAAcL,IAAI,CAACW,IAAI,IAAIF;IAC7B;AACF"}