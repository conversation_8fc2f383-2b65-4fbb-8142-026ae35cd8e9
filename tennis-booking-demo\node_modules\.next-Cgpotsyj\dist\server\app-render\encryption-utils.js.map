{"version": 3, "sources": ["../../../src/server/app-render/encryption-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n"], "names": ["arrayBufferToString", "decrypt", "encrypt", "getActionEncryptionKey", "getClientReferenceManifestForRsc", "getServerModuleMap", "setReferenceManifestsSingleton", "stringToUint8Array", "__next_loaded_action_key", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "length", "arr", "charCodeAt", "key", "iv", "data", "crypto", "subtle", "name", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "normalizeAppPath", "serverActionsManifestSingleton", "InvariantError", "workStore", "workAsyncStorage", "getStore", "mergeClientReferenceManifests", "route", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping"], "mappings": ";;;;;;;;;;;;;;;;;;;;;IAYgBA,mBAAmB;eAAnBA;;IA0CAC,OAAO;eAAPA;;IAXAC,OAAO;eAAPA;;IA6HMC,sBAAsB;eAAtBA;;IAxCNC,gCAAgC;eAAhCA;;IApBAC,kBAAkB;eAAlBA;;IAnCAC,8BAA8B;eAA9BA;;IAzCAC,kBAAkB;eAAlBA;;;gCA1Be;0BACE;0CACA;AAEjC,IAAIC;AAEG,SAASR,oBACdS,MAAiD;IAEjD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASV,mBAAmBU,MAAc;IAC/C,MAAML,MAAMK,OAAOE,MAAM;IACzB,MAAMC,MAAM,IAAIT,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BE,GAAG,CAACF,EAAE,GAAGD,OAAOI,UAAU,CAACH;IAC7B;IAEA,OAAOE;AACT;AAEO,SAASlB,QAAQoB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACxB,OAAO,CAC1B;QACEyB,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASvB,QAAQqB,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACzB,OAAO,CAC1B;QACE0B,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMI,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASxB,+BAA+B,EAC7CyB,IAAI,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAYhB;QAEyCC;IADxC,mBAAmB;IACnB,MAAMC,mCAAkCD,gDAAAA,UAAU,CAChDP,kCACD,qBAFuCO,8CAErCC,+BAA+B;IAIlC,mBAAmB;IACnBD,UAAU,CAACP,kCAAkC,GAAG;QAC9CQ,iCAAiC;YAC/B,GAAGA,+BAA+B;YAClC,CAACC,IAAAA,0BAAgB,EAACN,MAAM,EAAEC;QAC5B;QACAC;QACAC;IACF;AACF;AAEO,SAAS7B;IACd,MAAMiC,iCAAiC,AAACH,UAAkB,CACxDP,kCACD;IAUD,IAAI,CAACU,gCAAgC;QACnC,MAAM,qBAA0D,CAA1D,IAAIC,8BAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,OAAOD,+BAA+BJ,eAAe;AACvD;AAEO,SAAS9B;IACd,MAAMkC,iCAAiC,AAACH,UAAkB,CACxDP,kCACD;IAMD,IAAI,CAACU,gCAAgC;QACnC,MAAM,qBAA0D,CAA1D,IAAIC,8BAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM,EAAEH,+BAA+B,EAAE,GAAGE;IAC5C,MAAME,YAAYC,0CAAgB,CAACC,QAAQ;IAE3C,IAAI,CAACF,WAAW;QACd,0EAA0E;QAC1E,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,aAAa;QACb,OAAOG,8BAA8BP;IACvC;IAEA,MAAMJ,0BACJI,+BAA+B,CAACI,UAAUI,KAAK,CAAC;IAElD,IAAI,CAACZ,yBAAyB;QAC5B,MAAM,qBAEL,CAFK,IAAIO,8BAAc,CACtB,CAAC,sCAAsC,EAAEC,UAAUI,KAAK,CAAC,CAAC,CAAC,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOZ;AACT;AAEO,eAAe7B;IACpB,IAAIK,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAM8B,iCAAiC,AAACH,UAAkB,CACxDP,kCACD;IAID,IAAI,CAACU,gCAAgC;QACnC,MAAM,qBAA0D,CAA1D,IAAIC,8BAAc,CAAC,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAMM,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CV,+BAA+BL,qBAAqB,CAACgB,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,qBAA+D,CAA/D,IAAIX,8BAAc,CAAC,8CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEA/B,2BAA2B,MAAMiB,OAAOC,MAAM,CAACyB,SAAS,CACtD,OACA5C,mBAAmB6C,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAOrC;AACT;AAEA,SAASmC,8BACPP,+BAEC;IAED,MAAMiB,2BAA2BC,OAAOC,MAAM,CAC5CnB;IAGF,MAAMoB,gCAA+D;QACnEC,eAAe,CAAC;QAChBC,sBAAsB,CAAC;QACvBC,kBAAkB,CAAC;IACrB;IAEA,KAAK,MAAM3B,2BAA2BqB,yBAA0B;QAC9DG,8BAA8BC,aAAa,GAAG;YAC5C,GAAGD,8BAA8BC,aAAa;YAC9C,GAAGzB,wBAAwByB,aAAa;QAC1C;QACAD,8BAA8BE,oBAAoB,GAAG;YACnD,GAAGF,8BAA8BE,oBAAoB;YACrD,GAAG1B,wBAAwB0B,oBAAoB;QACjD;QACAF,8BAA8BG,gBAAgB,GAAG;YAC/C,GAAGH,8BAA8BG,gBAAgB;YACjD,GAAG3B,wBAAwB2B,gBAAgB;QAC7C;IACF;IAEA,OAAOH;AACT"}