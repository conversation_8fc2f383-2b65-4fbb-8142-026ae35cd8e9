export { createTemporaryReferenceSet, renderToReadableStream, decodeReply, decodeAction, decodeFormState, } from 'react-server-dom-webpack/server.edge';
export { unstable_prerender as prerender } from 'react-server-dom-webpack/static.edge';
import LayoutRouter from '../../client/components/layout-router';
import RenderFromTemplateContext from '../../client/components/render-from-template-context';
import { workAsyncStorage } from '../app-render/work-async-storage.external';
import { workUnitAsyncStorage } from './work-unit-async-storage.external';
import { actionAsyncStorage } from '../app-render/action-async-storage.external';
import { ClientPageRoot } from '../../client/components/client-page';
import { ClientSegmentRoot } from '../../client/components/client-segment';
import { createServerSearchParamsForServerPage, createPrerenderSearchParamsForClientPage } from '../request/search-params';
import { createServerParamsForServerSegment, createPrerenderParamsForClientSegment } from '../request/params';
import * as serverHooks from '../../client/components/hooks-server-context';
import { HTTPAccessFallbackBoundary } from '../../client/components/http-access-fallback/error-boundary';
import { createMetadataComponents } from '../../lib/metadata/metadata';
import '../../client/components/error-boundary';
import { MetadataBoundary, ViewportBoundary, OutletBoundary } from '../../client/components/metadata/metadata-boundary';
import { preloadStyle, preloadFont, preconnect } from './rsc/preloads';
import { Postpone } from './rsc/postpone';
import { taintObjectReference } from './rsc/taint';
export { collectSegmentData } from './collect-segment-data';
declare function patchFetch(): void;
export { LayoutRouter, RenderFromTemplateContext, workAsyncStorage, workUnitAsyncStorage, actionAsyncStorage, createServerSearchParamsForServerPage, createPrerenderSearchParamsForClientPage, createServerParamsForServerSegment, createPrerenderParamsForClientSegment, serverHooks, preloadStyle, preloadFont, preconnect, Postpone, MetadataBoundary, ViewportBoundary, OutletBoundary, taintObjectReference, ClientPageRoot, ClientSegmentRoot, HTTPAccessFallbackBoundary, patchFetch, createMetadataComponents, };
