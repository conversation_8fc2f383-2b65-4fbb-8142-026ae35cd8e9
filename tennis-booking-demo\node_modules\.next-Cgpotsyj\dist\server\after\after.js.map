{"version": 3, "sources": ["../../../src/server/after/after.ts"], "sourcesContent": ["import { workAsyncStorage } from '../app-render/work-async-storage.external'\n\nexport type AfterTask<T = unknown> = Promise<T> | AfterCallback<T>\nexport type AfterCallback<T = unknown> = () => T | Promise<T>\n\n/**\n * This function allows you to schedule callbacks to be executed after the current request finishes.\n */\nexport function after<T>(task: AfterTask<T>): void {\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // TODO(after): the linked docs page talks about *dynamic* APIs, which after soon won't be anymore\n    throw new Error(\n      '`after` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context'\n    )\n  }\n\n  const { afterContext } = workStore\n  return afterContext.after(task)\n}\n"], "names": ["after", "task", "workStore", "workAsyncStorage", "getStore", "Error", "afterContext"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;0CARiB;AAQ1B,SAASA,MAASC,IAAkB;IACzC,MAAMC,YAAYC,0CAAgB,CAACC,QAAQ;IAE3C,IAAI,CAACF,WAAW;QACd,kGAAkG;QAClG,MAAM,qBAEL,CAFK,IAAIG,MACR,2HADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM,EAAEC,YAAY,EAAE,GAAGJ;IACzB,OAAOI,aAAaN,KAAK,CAACC;AAC5B"}