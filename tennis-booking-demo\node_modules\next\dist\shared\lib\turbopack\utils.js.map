{"version": 3, "sources": ["../../../../src/shared/lib/turbopack/utils.ts"], "sourcesContent": ["import type {\n  Issue,\n  StyledString,\n  TurbopackResult,\n} from '../../../build/swc/types'\n\nimport { bold, green, magenta, red } from '../../../lib/picocolors'\nimport isInternal from '../is-internal'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../magic-identifier'\nimport type { EntryKey } from './entry-key'\nimport * as Log from '../../../build/output/log'\nimport type { NextConfigComplete } from '../../../server/config-shared'\nimport loadJsConfig from '../../../build/load-jsconfig'\nimport { eventErrorThrown } from '../../../telemetry/events'\nimport { traceGlobals } from '../../../trace/shared'\n\ntype IssueKey = `${Issue['severity']}-${Issue['filePath']}-${string}-${string}`\nexport type IssuesMap = Map<IssueKey, Issue>\nexport type EntryIssuesMap = Map<EntryKey, IssuesMap>\nexport type TopLevelIssuesMap = IssuesMap\n\n// An error generated from emitted Turbopack issues. This can include build\n// errors caused by issues with user code.\nexport class ModuleBuildError extends Error {\n  name = 'ModuleBuildError'\n}\n\n// An error caused by an internal issue in Turbopack. These should be written\n// to a log file and details should not be shown to the user.\nexport class TurbopackInternalError extends Error {\n  name = 'TurbopackInternalError'\n\n  // Manually set this as this isn't statically determinable\n  __NEXT_ERROR_CODE = 'TurbopackInternalError'\n\n  static createAndRecordTelemetry(cause: Error) {\n    const error = new TurbopackInternalError(cause)\n\n    const telemetry = traceGlobals.get('telemetry')\n    if (telemetry) {\n      telemetry.record(eventErrorThrown(error))\n    } else {\n      console.error('Expected `telemetry` to be set in globals')\n    }\n\n    return error\n  }\n\n  constructor(cause: Error) {\n    super(cause.message)\n    this.stack = cause.stack\n  }\n}\n\n/**\n * Thin stopgap workaround layer to mimic existing wellknown-errors-plugin in webpack's build\n * to emit certain type of errors into cli.\n */\nexport function isWellKnownError(issue: Issue): boolean {\n  const { title } = issue\n  const formattedTitle = renderStyledStringToErrorAnsi(title)\n  // TODO: add more well known errors\n  if (\n    formattedTitle.includes('Module not found') ||\n    formattedTitle.includes('Unknown module type')\n  ) {\n    return true\n  }\n\n  return false\n}\n\nexport function getIssueKey(issue: Issue): IssueKey {\n  return `${issue.severity}-${issue.filePath}-${JSON.stringify(\n    issue.title\n  )}-${JSON.stringify(issue.description)}`\n}\n\nexport async function getTurbopackJsConfig(\n  dir: string,\n  nextConfig: NextConfigComplete\n) {\n  const { jsConfig } = await loadJsConfig(dir, nextConfig)\n  return jsConfig ?? { compilerOptions: {} }\n}\n\nexport function processIssues(\n  currentEntryIssues: EntryIssuesMap,\n  key: EntryKey,\n  result: TurbopackResult,\n  throwIssue: boolean,\n  logErrors: boolean\n) {\n  const newIssues = new Map<IssueKey, Issue>()\n  currentEntryIssues.set(key, newIssues)\n\n  const relevantIssues = new Set()\n\n  for (const issue of result.issues) {\n    if (\n      issue.severity !== 'error' &&\n      issue.severity !== 'fatal' &&\n      issue.severity !== 'warning'\n    )\n      continue\n\n    const issueKey = getIssueKey(issue)\n    newIssues.set(issueKey, issue)\n\n    if (issue.severity !== 'warning') {\n      if (throwIssue) {\n        const formatted = formatIssue(issue)\n        relevantIssues.add(formatted)\n      }\n      // if we throw the issue it will most likely get handed and logged elsewhere\n      else if (logErrors && isWellKnownError(issue)) {\n        const formatted = formatIssue(issue)\n        Log.error(formatted)\n      }\n    }\n  }\n\n  if (relevantIssues.size && throwIssue) {\n    throw new ModuleBuildError([...relevantIssues].join('\\n\\n'))\n  }\n}\n\nexport function formatIssue(issue: Issue) {\n  const { filePath, title, description, source } = issue\n  let { documentationLink } = issue\n  let formattedTitle = renderStyledStringToErrorAnsi(title).replace(\n    /\\n/g,\n    '\\n    '\n  )\n\n  // TODO: Use error codes to identify these\n  // TODO: Generalize adapting Turbopack errors to Next.js errors\n  if (formattedTitle.includes('Module not found')) {\n    // For compatiblity with webpack\n    // TODO: include columns in webpack errors.\n    documentationLink = 'https://nextjs.org/docs/messages/module-not-found'\n  }\n\n  let formattedFilePath = filePath\n    .replace('[project]/', './')\n    .replaceAll('/./', '/')\n    .replace('\\\\\\\\?\\\\', '')\n\n  let message = ''\n\n  if (source && source.range) {\n    const { start } = source.range\n    message = `${formattedFilePath}:${start.line + 1}:${\n      start.column + 1\n    }\\n${formattedTitle}`\n  } else if (formattedFilePath) {\n    message = `${formattedFilePath}\\n${formattedTitle}`\n  } else {\n    message = formattedTitle\n  }\n  message += '\\n'\n\n  if (\n    source?.range &&\n    source.source.content &&\n    // ignore Next.js/React internals, as these can often be huge bundled files.\n    !isInternal(filePath)\n  ) {\n    const { start, end } = source.range\n    const { codeFrameColumns } = require('next/dist/compiled/babel/code-frame')\n\n    message +=\n      codeFrameColumns(\n        source.source.content,\n        {\n          start: {\n            line: start.line + 1,\n            column: start.column + 1,\n          },\n          end: {\n            line: end.line + 1,\n            column: end.column + 1,\n          },\n        },\n        { forceColor: true }\n      ).trim() + '\\n\\n'\n  }\n\n  if (description) {\n    message += renderStyledStringToErrorAnsi(description) + '\\n\\n'\n  }\n\n  // TODO: make it possible to enable this for debugging, but not in tests.\n  // if (detail) {\n  //   message += renderStyledStringToErrorAnsi(detail) + '\\n\\n'\n  // }\n\n  // TODO: Include a trace from the issue.\n\n  if (documentationLink) {\n    message += documentationLink + '\\n\\n'\n  }\n\n  return message\n}\n\nexport function isRelevantWarning(issue: Issue): boolean {\n  return issue.severity === 'warning' && !isNodeModulesIssue(issue)\n}\n\nfunction isNodeModulesIssue(issue: Issue): boolean {\n  if (issue.severity === 'warning' && issue.stage === 'config') {\n    // Override for the externalize issue\n    // `Package foo (serverExternalPackages or default list) can't be external`\n    if (\n      renderStyledStringToErrorAnsi(issue.title).includes(\"can't be external\")\n    ) {\n      return false\n    }\n  }\n\n  return (\n    issue.severity === 'warning' &&\n    (issue.filePath.match(/^(?:.*[\\\\/])?node_modules(?:[\\\\/].*)?$/) !== null ||\n      // Ignore Next.js itself when running next directly in the monorepo where it is not inside\n      // node_modules anyway.\n      // TODO(mischnic) prevent matches when this is published to npm\n      issue.filePath.startsWith('[project]/packages/next/'))\n  )\n}\n\nexport function renderStyledStringToErrorAnsi(string: StyledString): string {\n  function decodeMagicIdentifiers(str: string): string {\n    return str.replaceAll(MAGIC_IDENTIFIER_REGEX, (ident) => {\n      try {\n        return magenta(`{${decodeMagicIdentifier(ident)}}`)\n      } catch (e) {\n        return magenta(`{${ident} (decoding failed: ${e})}`)\n      }\n    })\n  }\n\n  switch (string.type) {\n    case 'text':\n      return decodeMagicIdentifiers(string.value)\n    case 'strong':\n      return bold(red(decodeMagicIdentifiers(string.value)))\n    case 'code':\n      return green(decodeMagicIdentifiers(string.value))\n    case 'line':\n      return string.value.map(renderStyledStringToErrorAnsi).join('')\n    case 'stack':\n      return string.value.map(renderStyledStringToErrorAnsi).join('\\n')\n    default:\n      throw new Error('Unknown StyledString type', string)\n  }\n}\n\nexport function isPersistentCachingEnabled(\n  config: NextConfigComplete\n): boolean {\n  return config.experimental?.turbopackPersistentCaching || false\n}\n"], "names": ["ModuleBuildError", "TurbopackInternalError", "formatIssue", "getIssueKey", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isRelevantWarning", "isWellKnownError", "processIssues", "renderStyledStringToErrorAnsi", "Error", "name", "createAndRecordTelemetry", "cause", "error", "telemetry", "traceGlobals", "get", "record", "eventErrorThrown", "console", "constructor", "message", "__NEXT_ERROR_CODE", "stack", "issue", "title", "formattedTitle", "includes", "severity", "filePath", "JSON", "stringify", "description", "dir", "nextConfig", "jsConfig", "loadJsConfig", "compilerOptions", "currentEntryIssues", "key", "result", "throwIssue", "logErrors", "newIssues", "Map", "set", "relevantIssues", "Set", "issues", "issue<PERSON><PERSON>", "formatted", "add", "Log", "size", "join", "source", "documentationLink", "replace", "formattedFilePath", "replaceAll", "range", "start", "line", "column", "content", "isInternal", "end", "codeFrameColumns", "require", "forceColor", "trim", "isNodeModulesIssue", "stage", "match", "startsWith", "string", "decodeMagicIdentifiers", "str", "MAGIC_IDENTIFIER_REGEX", "ident", "magenta", "decodeMagicIdentifier", "e", "type", "value", "bold", "red", "green", "map", "config", "experimental", "turbopackPersistentCaching"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA0BaA,gBAAgB;eAAhBA;;IAMAC,sBAAsB;eAAtBA;;IAkGGC,WAAW;eAAXA;;IAvDAC,WAAW;eAAXA;;IAMMC,oBAAoB;eAApBA;;IAoLNC,0BAA0B;eAA1BA;;IApDAC,iBAAiB;eAAjBA;;IApJAC,gBAAgB;eAAhBA;;IA4BAC,aAAa;eAAbA;;IAiJAC,6BAA6B;eAA7BA;;;;;4BApO0B;qEACnB;iCAIhB;+DAEc;uEAEI;wBACQ;wBACJ;AAStB,MAAMT,yBAAyBU;;QAA/B,qBACLC,OAAO;;AACT;AAIO,MAAMV,+BAA+BS;IAM1C,OAAOE,yBAAyBC,KAAY,EAAE;QAC5C,MAAMC,QAAQ,IAAIb,uBAAuBY;QAEzC,MAAME,YAAYC,oBAAY,CAACC,GAAG,CAAC;QACnC,IAAIF,WAAW;YACbA,UAAUG,MAAM,CAACC,IAAAA,wBAAgB,EAACL;QACpC,OAAO;YACLM,QAAQN,KAAK,CAAC;QAChB;QAEA,OAAOA;IACT;IAEAO,YAAYR,KAAY,CAAE;QACxB,KAAK,CAACA,MAAMS,OAAO,QAnBrBX,OAAO,0BAEP,0DAA0D;aAC1DY,oBAAoB;QAiBlB,IAAI,CAACC,KAAK,GAAGX,MAAMW,KAAK;IAC1B;AACF;AAMO,SAASjB,iBAAiBkB,KAAY;IAC3C,MAAM,EAAEC,KAAK,EAAE,GAAGD;IAClB,MAAME,iBAAiBlB,8BAA8BiB;IACrD,mCAAmC;IACnC,IACEC,eAAeC,QAAQ,CAAC,uBACxBD,eAAeC,QAAQ,CAAC,wBACxB;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,SAASzB,YAAYsB,KAAY;IACtC,OAAO,AAAGA,MAAMI,QAAQ,GAAC,MAAGJ,MAAMK,QAAQ,GAAC,MAAGC,KAAKC,SAAS,CAC1DP,MAAMC,KAAK,IACX,MAAGK,KAAKC,SAAS,CAACP,MAAMQ,WAAW;AACvC;AAEO,eAAe7B,qBACpB8B,GAAW,EACXC,UAA8B;IAE9B,MAAM,EAAEC,QAAQ,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EAACH,KAAKC;IAC7C,OAAOC,mBAAAA,WAAY;QAAEE,iBAAiB,CAAC;IAAE;AAC3C;AAEO,SAAS9B,cACd+B,kBAAkC,EAClCC,GAAa,EACbC,MAAuB,EACvBC,UAAmB,EACnBC,SAAkB;IAElB,MAAMC,YAAY,IAAIC;IACtBN,mBAAmBO,GAAG,CAACN,KAAKI;IAE5B,MAAMG,iBAAiB,IAAIC;IAE3B,KAAK,MAAMvB,SAASgB,OAAOQ,MAAM,CAAE;QACjC,IACExB,MAAMI,QAAQ,KAAK,WACnBJ,MAAMI,QAAQ,KAAK,WACnBJ,MAAMI,QAAQ,KAAK,WAEnB;QAEF,MAAMqB,WAAW/C,YAAYsB;QAC7BmB,UAAUE,GAAG,CAACI,UAAUzB;QAExB,IAAIA,MAAMI,QAAQ,KAAK,WAAW;YAChC,IAAIa,YAAY;gBACd,MAAMS,YAAYjD,YAAYuB;gBAC9BsB,eAAeK,GAAG,CAACD;YACrB,OAEK,IAAIR,aAAapC,iBAAiBkB,QAAQ;gBAC7C,MAAM0B,YAAYjD,YAAYuB;gBAC9B4B,KAAIvC,KAAK,CAACqC;YACZ;QACF;IACF;IAEA,IAAIJ,eAAeO,IAAI,IAAIZ,YAAY;QACrC,MAAM,qBAAsD,CAAtD,IAAI1C,iBAAiB;eAAI+C;SAAe,CAACQ,IAAI,CAAC,UAA9C,qBAAA;mBAAA;wBAAA;0BAAA;QAAqD;IAC7D;AACF;AAEO,SAASrD,YAAYuB,KAAY;IACtC,MAAM,EAAEK,QAAQ,EAAEJ,KAAK,EAAEO,WAAW,EAAEuB,MAAM,EAAE,GAAG/B;IACjD,IAAI,EAAEgC,iBAAiB,EAAE,GAAGhC;IAC5B,IAAIE,iBAAiBlB,8BAA8BiB,OAAOgC,OAAO,CAC/D,OACA;IAGF,0CAA0C;IAC1C,+DAA+D;IAC/D,IAAI/B,eAAeC,QAAQ,CAAC,qBAAqB;QAC/C,gCAAgC;QAChC,2CAA2C;QAC3C6B,oBAAoB;IACtB;IAEA,IAAIE,oBAAoB7B,SACrB4B,OAAO,CAAC,cAAc,MACtBE,UAAU,CAAC,OAAO,KAClBF,OAAO,CAAC,WAAW;IAEtB,IAAIpC,UAAU;IAEd,IAAIkC,UAAUA,OAAOK,KAAK,EAAE;QAC1B,MAAM,EAAEC,KAAK,EAAE,GAAGN,OAAOK,KAAK;QAC9BvC,UAAU,AAAGqC,oBAAkB,MAAGG,CAAAA,MAAMC,IAAI,GAAG,CAAA,IAAE,MAC/CD,CAAAA,MAAME,MAAM,GAAG,CAAA,IAChB,OAAIrC;IACP,OAAO,IAAIgC,mBAAmB;QAC5BrC,UAAU,AAAGqC,oBAAkB,OAAIhC;IACrC,OAAO;QACLL,UAAUK;IACZ;IACAL,WAAW;IAEX,IACEkC,CAAAA,0BAAAA,OAAQK,KAAK,KACbL,OAAOA,MAAM,CAACS,OAAO,IACrB,4EAA4E;IAC5E,CAACC,IAAAA,mBAAU,EAACpC,WACZ;QACA,MAAM,EAAEgC,KAAK,EAAEK,GAAG,EAAE,GAAGX,OAAOK,KAAK;QACnC,MAAM,EAAEO,gBAAgB,EAAE,GAAGC,QAAQ;QAErC/C,WACE8C,iBACEZ,OAAOA,MAAM,CAACS,OAAO,EACrB;YACEH,OAAO;gBACLC,MAAMD,MAAMC,IAAI,GAAG;gBACnBC,QAAQF,MAAME,MAAM,GAAG;YACzB;YACAG,KAAK;gBACHJ,MAAMI,IAAIJ,IAAI,GAAG;gBACjBC,QAAQG,IAAIH,MAAM,GAAG;YACvB;QACF,GACA;YAAEM,YAAY;QAAK,GACnBC,IAAI,KAAK;IACf;IAEA,IAAItC,aAAa;QACfX,WAAWb,8BAA8BwB,eAAe;IAC1D;IAEA,yEAAyE;IACzE,gBAAgB;IAChB,8DAA8D;IAC9D,IAAI;IAEJ,wCAAwC;IAExC,IAAIwB,mBAAmB;QACrBnC,WAAWmC,oBAAoB;IACjC;IAEA,OAAOnC;AACT;AAEO,SAAShB,kBAAkBmB,KAAY;IAC5C,OAAOA,MAAMI,QAAQ,KAAK,aAAa,CAAC2C,mBAAmB/C;AAC7D;AAEA,SAAS+C,mBAAmB/C,KAAY;IACtC,IAAIA,MAAMI,QAAQ,KAAK,aAAaJ,MAAMgD,KAAK,KAAK,UAAU;QAC5D,qCAAqC;QACrC,2EAA2E;QAC3E,IACEhE,8BAA8BgB,MAAMC,KAAK,EAAEE,QAAQ,CAAC,sBACpD;YACA,OAAO;QACT;IACF;IAEA,OACEH,MAAMI,QAAQ,KAAK,aAClBJ,CAAAA,MAAMK,QAAQ,CAAC4C,KAAK,CAAC,8CAA8C,QAClE,0FAA0F;IAC1F,uBAAuB;IACvB,+DAA+D;IAC/DjD,MAAMK,QAAQ,CAAC6C,UAAU,CAAC,2BAA0B;AAE1D;AAEO,SAASlE,8BAA8BmE,MAAoB;IAChE,SAASC,uBAAuBC,GAAW;QACzC,OAAOA,IAAIlB,UAAU,CAACmB,uCAAsB,EAAE,CAACC;YAC7C,IAAI;gBACF,OAAOC,IAAAA,mBAAO,EAAC,AAAC,MAAGC,IAAAA,sCAAqB,EAACF,SAAO;YAClD,EAAE,OAAOG,GAAG;gBACV,OAAOF,IAAAA,mBAAO,EAAC,AAAC,MAAGD,QAAM,wBAAqBG,IAAE;YAClD;QACF;IACF;IAEA,OAAQP,OAAOQ,IAAI;QACjB,KAAK;YACH,OAAOP,uBAAuBD,OAAOS,KAAK;QAC5C,KAAK;YACH,OAAOC,IAAAA,gBAAI,EAACC,IAAAA,eAAG,EAACV,uBAAuBD,OAAOS,KAAK;QACrD,KAAK;YACH,OAAOG,IAAAA,iBAAK,EAACX,uBAAuBD,OAAOS,KAAK;QAClD,KAAK;YACH,OAAOT,OAAOS,KAAK,CAACI,GAAG,CAAChF,+BAA+B8C,IAAI,CAAC;QAC9D,KAAK;YACH,OAAOqB,OAAOS,KAAK,CAACI,GAAG,CAAChF,+BAA+B8C,IAAI,CAAC;QAC9D;YACE,MAAM,qBAA8C,CAA9C,IAAI7C,MAAM,6BAA6BkE,SAAvC,qBAAA;uBAAA;4BAAA;8BAAA;YAA6C;IACvD;AACF;AAEO,SAASvE,2BACdqF,MAA0B;QAEnBA;IAAP,OAAOA,EAAAA,uBAAAA,OAAOC,YAAY,qBAAnBD,qBAAqBE,0BAA0B,KAAI;AAC5D"}