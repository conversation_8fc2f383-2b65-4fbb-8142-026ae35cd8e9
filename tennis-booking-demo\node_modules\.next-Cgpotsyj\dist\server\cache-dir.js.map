{"version": 3, "sources": ["../../src/server/cache-dir.ts"], "sourcesContent": ["import path from 'path'\nimport isDockerFunction from 'next/dist/compiled/is-docker'\n\nexport function getStorageDirectory(distDir: string): string | undefined {\n  const isLikelyEphemeral = isDockerFunction()\n\n  if (isLikelyEphemeral) {\n    return undefined\n  }\n  return path.join(distDir, 'cache')\n}\n"], "names": ["getStorageDirectory", "distDir", "isLikelyEphemeral", "isDockerFunction", "undefined", "path", "join"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;6DAHC;iEACY;;;;;;AAEtB,SAASA,oBAAoBC,OAAe;IACjD,MAAMC,oBAAoBC,IAAAA,iBAAgB;IAE1C,IAAID,mBAAmB;QACrB,OAAOE;IACT;IACA,OAAOC,aAAI,CAACC,IAAI,CAACL,SAAS;AAC5B"}