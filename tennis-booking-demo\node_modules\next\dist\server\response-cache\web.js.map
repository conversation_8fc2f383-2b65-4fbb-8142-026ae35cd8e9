{"version": 3, "sources": ["../../../src/server/response-cache/web.ts"], "sourcesContent": ["import { DetachedPromise } from '../../lib/detached-promise'\nimport type { ResponseCacheEntry, ResponseGenerator } from './types'\n\n/**\n * In the web server, there is currently no incremental cache provided and we\n * always SSR the page.\n */\nexport default class WebResponseCache {\n  pendingResponses: Map<string, Promise<ResponseCacheEntry | null>>\n  previousCacheItem?: {\n    key: string\n    entry: ResponseCacheEntry | null\n    expiresAt: number\n  }\n  minimalMode?: boolean\n\n  constructor(minimalMode: boolean) {\n    this.pendingResponses = new Map()\n    // this is a hack to avoid Webpack knowing this is equal to this.minimalMode\n    // because we replace this.minimalMode to true in production bundles.\n    Object.assign(this, { minimalMode })\n  }\n\n  public get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: any\n    }\n  ): Promise<ResponseCacheEntry | null> {\n    // ensure on-demand revalidate doesn't block normal requests\n    const pendingResponseKey = key\n      ? `${key}-${context.isOnDemandRevalidate ? '1' : '0'}`\n      : null\n\n    const pendingResponse = pendingResponseKey\n      ? this.pendingResponses.get(pendingResponseKey)\n      : null\n    if (pendingResponse) {\n      return pendingResponse\n    }\n\n    const {\n      promise,\n      resolve: resolver,\n      reject: rejecter,\n    } = new DetachedPromise<ResponseCacheEntry | null>()\n    if (pendingResponseKey) {\n      this.pendingResponses.set(pendingResponseKey, promise)\n    }\n\n    let hasResolved = false\n    const resolve = (cacheEntry: ResponseCacheEntry | null) => {\n      if (pendingResponseKey) {\n        // Ensure all reads from the cache get the latest value.\n        this.pendingResponses.set(\n          pendingResponseKey,\n          Promise.resolve(cacheEntry)\n        )\n      }\n      if (!hasResolved) {\n        hasResolved = true\n        resolver(cacheEntry)\n      }\n    }\n\n    // we keep the previous cache entry around to leverage\n    // when the incremental cache is disabled in minimal mode\n    if (\n      pendingResponseKey &&\n      this.minimalMode &&\n      this.previousCacheItem?.key === pendingResponseKey &&\n      this.previousCacheItem.expiresAt > Date.now()\n    ) {\n      resolve(this.previousCacheItem.entry)\n      this.pendingResponses.delete(pendingResponseKey)\n      return promise\n    }\n\n    // We wait to do any async work until after we've added our promise to\n    // `pendingResponses` to ensure that any any other calls will reuse the\n    // same promise until we've fully finished our work.\n    ;(async () => {\n      try {\n        const cacheEntry = await responseGenerator({ hasResolved })\n        const resolveValue =\n          cacheEntry === null\n            ? null\n            : {\n                ...cacheEntry,\n                isMiss: true,\n              }\n\n        // for on-demand revalidate wait to resolve until cache is set\n        if (!context.isOnDemandRevalidate) {\n          resolve(resolveValue)\n        }\n\n        if (key && cacheEntry && cacheEntry.cacheControl) {\n          this.previousCacheItem = {\n            key: pendingResponseKey || key,\n            entry: cacheEntry,\n            expiresAt: Date.now() + 1000,\n          }\n        } else {\n          this.previousCacheItem = undefined\n        }\n\n        if (context.isOnDemandRevalidate) {\n          resolve(resolveValue)\n        }\n      } catch (err) {\n        // while revalidating in the background we can't reject as\n        // we already resolved the cache entry so log the error here\n        if (hasResolved) {\n          console.error(err)\n        } else {\n          rejecter(err as Error)\n        }\n      } finally {\n        if (pendingResponseKey) {\n          this.pendingResponses.delete(pendingResponseKey)\n        }\n      }\n    })()\n    return promise\n  }\n}\n"], "names": ["WebResponseCache", "constructor", "minimalMode", "pendingResponses", "Map", "Object", "assign", "get", "key", "responseGenerator", "context", "pendingResponseKey", "isOnDemandRevalidate", "pendingResponse", "promise", "resolve", "resolver", "reject", "rejecter", "Detached<PERSON>romise", "set", "hasResolved", "cacheEntry", "Promise", "previousCacheItem", "expiresAt", "Date", "now", "entry", "delete", "resolveValue", "isMiss", "cacheControl", "undefined", "err", "console", "error"], "mappings": ";;;;+BAGA;;;CAGC,GACD;;;eAAqBA;;;iCAPW;AAOjB,MAAMA;IASnBC,YAAYC,WAAoB,CAAE;QAChC,IAAI,CAACC,gBAAgB,GAAG,IAAIC;QAC5B,4EAA4E;QAC5E,qEAAqE;QACrEC,OAAOC,MAAM,CAAC,IAAI,EAAE;YAAEJ;QAAY;IACpC;IAEOK,IACLC,GAAkB,EAClBC,iBAAoC,EACpCC,OAIC,EACmC;YA0ClC;QAzCF,4DAA4D;QAC5D,MAAMC,qBAAqBH,MACvB,GAAGA,IAAI,CAAC,EAAEE,QAAQE,oBAAoB,GAAG,MAAM,KAAK,GACpD;QAEJ,MAAMC,kBAAkBF,qBACpB,IAAI,CAACR,gBAAgB,CAACI,GAAG,CAACI,sBAC1B;QACJ,IAAIE,iBAAiB;YACnB,OAAOA;QACT;QAEA,MAAM,EACJC,OAAO,EACPC,SAASC,QAAQ,EACjBC,QAAQC,QAAQ,EACjB,GAAG,IAAIC,gCAAe;QACvB,IAAIR,oBAAoB;YACtB,IAAI,CAACR,gBAAgB,CAACiB,GAAG,CAACT,oBAAoBG;QAChD;QAEA,IAAIO,cAAc;QAClB,MAAMN,UAAU,CAACO;YACf,IAAIX,oBAAoB;gBACtB,wDAAwD;gBACxD,IAAI,CAACR,gBAAgB,CAACiB,GAAG,CACvBT,oBACAY,QAAQR,OAAO,CAACO;YAEpB;YACA,IAAI,CAACD,aAAa;gBAChBA,cAAc;gBACdL,SAASM;YACX;QACF;QAEA,sDAAsD;QACtD,yDAAyD;QACzD,IACEX,sBACA,IAAI,CAACT,WAAW,IAChB,EAAA,0BAAA,IAAI,CAACsB,iBAAiB,qBAAtB,wBAAwBhB,GAAG,MAAKG,sBAChC,IAAI,CAACa,iBAAiB,CAACC,SAAS,GAAGC,KAAKC,GAAG,IAC3C;YACAZ,QAAQ,IAAI,CAACS,iBAAiB,CAACI,KAAK;YACpC,IAAI,CAACzB,gBAAgB,CAAC0B,MAAM,CAAClB;YAC7B,OAAOG;QACT;QAEA,sEAAsE;QACtE,uEAAuE;QACvE,oDAAoD;;QAClD,CAAA;YACA,IAAI;gBACF,MAAMQ,aAAa,MAAMb,kBAAkB;oBAAEY;gBAAY;gBACzD,MAAMS,eACJR,eAAe,OACX,OACA;oBACE,GAAGA,UAAU;oBACbS,QAAQ;gBACV;gBAEN,8DAA8D;gBAC9D,IAAI,CAACrB,QAAQE,oBAAoB,EAAE;oBACjCG,QAAQe;gBACV;gBAEA,IAAItB,OAAOc,cAAcA,WAAWU,YAAY,EAAE;oBAChD,IAAI,CAACR,iBAAiB,GAAG;wBACvBhB,KAAKG,sBAAsBH;wBAC3BoB,OAAON;wBACPG,WAAWC,KAAKC,GAAG,KAAK;oBAC1B;gBACF,OAAO;oBACL,IAAI,CAACH,iBAAiB,GAAGS;gBAC3B;gBAEA,IAAIvB,QAAQE,oBAAoB,EAAE;oBAChCG,QAAQe;gBACV;YACF,EAAE,OAAOI,KAAK;gBACZ,0DAA0D;gBAC1D,4DAA4D;gBAC5D,IAAIb,aAAa;oBACfc,QAAQC,KAAK,CAACF;gBAChB,OAAO;oBACLhB,SAASgB;gBACX;YACF,SAAU;gBACR,IAAIvB,oBAAoB;oBACtB,IAAI,CAACR,gBAAgB,CAAC0B,MAAM,CAAClB;gBAC/B;YACF;QACF,CAAA;QACA,OAAOG;IACT;AACF"}