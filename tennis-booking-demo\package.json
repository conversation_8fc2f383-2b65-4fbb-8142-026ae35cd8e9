{"name": "tennis-booking-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.15", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.5", "@eslint/eslintrc": "^3", "lucide-react": "^0.468.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4"}, "description": "This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}