{"name": "tennis-booking-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"framer-motion": "^11.18.2", "lightningcss": "^1.30.1", "next": "^15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^20.17.9", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9.17.0", "eslint-config-next": "15.3.5", "lucide-react": "^0.468.0", "postcss": "^8.4.49", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "description": "This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).", "main": "index.js", "keywords": [], "author": "", "license": "ISC"}