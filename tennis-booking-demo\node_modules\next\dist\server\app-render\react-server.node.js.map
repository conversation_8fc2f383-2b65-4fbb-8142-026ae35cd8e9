{"version": 3, "sources": ["../../../src/server/app-render/react-server.node.ts"], "sourcesContent": ["// This file should be opted into the react-server layer\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport {\n  createTemporaryReferenceSet,\n  decodeReply,\n  decodeReplyFromBusboy,\n  decodeAction,\n  decodeFormState,\n} from 'react-server-dom-webpack/server.node'\n"], "names": ["createTemporaryReferenceSet", "decodeAction", "decodeFormState", "decodeReply", "decodeReplyFromBusboy"], "mappings": "AAAA,wDAAwD;AAExD,6DAA6D;;;;;;;;;;;;;;;;;;;IAE3DA,2BAA2B;eAA3BA,uCAA2B;;IAG3BC,YAAY;eAAZA,wBAAY;;IACZC,eAAe;eAAfA,2BAAe;;IAHfC,WAAW;eAAXA,uBAAW;;IACXC,qBAAqB;eAArBA,iCAAqB;;;4BAGhB"}