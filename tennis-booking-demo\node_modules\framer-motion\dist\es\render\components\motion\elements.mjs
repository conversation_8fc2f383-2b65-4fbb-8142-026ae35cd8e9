"use client";
import { createMotionComponent } from './create.mjs';

/**
 * HTML components
 */
const MotionA = /*@__PURE__*/ createMotionComponent("a");
const MotionAbbr = /*@__PURE__*/ createMotionComponent("abbr");
const MotionAddress = /*@__PURE__*/ createMotionComponent("address");
const MotionArea = /*@__PURE__*/ createMotionComponent("area");
const MotionArticle = /*@__PURE__*/ createMotionComponent("article");
const MotionAside = /*@__PURE__*/ createMotionComponent("aside");
const MotionAudio = /*@__PURE__*/ createMotionComponent("audio");
const MotionB = /*@__PURE__*/ createMotionComponent("b");
const MotionBase = /*@__PURE__*/ createMotionComponent("base");
const MotionBdi = /*@__PURE__*/ createMotionComponent("bdi");
const MotionBdo = /*@__PURE__*/ createMotionComponent("bdo");
const MotionBig = /*@__PURE__*/ createMotionComponent("big");
const MotionBlockquote = 
/*@__PURE__*/ createMotionComponent("blockquote");
const MotionBody = /*@__PURE__*/ createMotionComponent("body");
const MotionButton = /*@__PURE__*/ createMotionComponent("button");
const MotionCanvas = /*@__PURE__*/ createMotionComponent("canvas");
const MotionCaption = /*@__PURE__*/ createMotionComponent("caption");
const MotionCite = /*@__PURE__*/ createMotionComponent("cite");
const MotionCode = /*@__PURE__*/ createMotionComponent("code");
const MotionCol = /*@__PURE__*/ createMotionComponent("col");
const MotionColgroup = /*@__PURE__*/ createMotionComponent("colgroup");
const MotionData = /*@__PURE__*/ createMotionComponent("data");
const MotionDatalist = /*@__PURE__*/ createMotionComponent("datalist");
const MotionDd = /*@__PURE__*/ createMotionComponent("dd");
const MotionDel = /*@__PURE__*/ createMotionComponent("del");
const MotionDetails = /*@__PURE__*/ createMotionComponent("details");
const MotionDfn = /*@__PURE__*/ createMotionComponent("dfn");
const MotionDialog = /*@__PURE__*/ createMotionComponent("dialog");
const MotionDiv = /*@__PURE__*/ createMotionComponent("div");
const MotionDl = /*@__PURE__*/ createMotionComponent("dl");
const MotionDt = /*@__PURE__*/ createMotionComponent("dt");
const MotionEm = /*@__PURE__*/ createMotionComponent("em");
const MotionEmbed = /*@__PURE__*/ createMotionComponent("embed");
const MotionFieldset = /*@__PURE__*/ createMotionComponent("fieldset");
const MotionFigcaption = 
/*@__PURE__*/ createMotionComponent("figcaption");
const MotionFigure = /*@__PURE__*/ createMotionComponent("figure");
const MotionFooter = /*@__PURE__*/ createMotionComponent("footer");
const MotionForm = /*@__PURE__*/ createMotionComponent("form");
const MotionH1 = /*@__PURE__*/ createMotionComponent("h1");
const MotionH2 = /*@__PURE__*/ createMotionComponent("h2");
const MotionH3 = /*@__PURE__*/ createMotionComponent("h3");
const MotionH4 = /*@__PURE__*/ createMotionComponent("h4");
const MotionH5 = /*@__PURE__*/ createMotionComponent("h5");
const MotionH6 = /*@__PURE__*/ createMotionComponent("h6");
const MotionHead = /*@__PURE__*/ createMotionComponent("head");
const MotionHeader = /*@__PURE__*/ createMotionComponent("header");
const MotionHgroup = /*@__PURE__*/ createMotionComponent("hgroup");
const MotionHr = /*@__PURE__*/ createMotionComponent("hr");
const MotionHtml = /*@__PURE__*/ createMotionComponent("html");
const MotionI = /*@__PURE__*/ createMotionComponent("i");
const MotionIframe = /*@__PURE__*/ createMotionComponent("iframe");
const MotionImg = /*@__PURE__*/ createMotionComponent("img");
const MotionInput = /*@__PURE__*/ createMotionComponent("input");
const MotionIns = /*@__PURE__*/ createMotionComponent("ins");
const MotionKbd = /*@__PURE__*/ createMotionComponent("kbd");
const MotionKeygen = /*@__PURE__*/ createMotionComponent("keygen");
const MotionLabel = /*@__PURE__*/ createMotionComponent("label");
const MotionLegend = /*@__PURE__*/ createMotionComponent("legend");
const MotionLi = /*@__PURE__*/ createMotionComponent("li");
const MotionLink = /*@__PURE__*/ createMotionComponent("link");
const MotionMain = /*@__PURE__*/ createMotionComponent("main");
const MotionMap = /*@__PURE__*/ createMotionComponent("map");
const MotionMark = /*@__PURE__*/ createMotionComponent("mark");
const MotionMenu = /*@__PURE__*/ createMotionComponent("menu");
const MotionMenuitem = /*@__PURE__*/ createMotionComponent("menuitem");
const MotionMeter = /*@__PURE__*/ createMotionComponent("meter");
const MotionNav = /*@__PURE__*/ createMotionComponent("nav");
const MotionObject = /*@__PURE__*/ createMotionComponent("object");
const MotionOl = /*@__PURE__*/ createMotionComponent("ol");
const MotionOptgroup = /*@__PURE__*/ createMotionComponent("optgroup");
const MotionOption = /*@__PURE__*/ createMotionComponent("option");
const MotionOutput = /*@__PURE__*/ createMotionComponent("output");
const MotionP = /*@__PURE__*/ createMotionComponent("p");
const MotionParam = /*@__PURE__*/ createMotionComponent("param");
const MotionPicture = /*@__PURE__*/ createMotionComponent("picture");
const MotionPre = /*@__PURE__*/ createMotionComponent("pre");
const MotionProgress = /*@__PURE__*/ createMotionComponent("progress");
const MotionQ = /*@__PURE__*/ createMotionComponent("q");
const MotionRp = /*@__PURE__*/ createMotionComponent("rp");
const MotionRt = /*@__PURE__*/ createMotionComponent("rt");
const MotionRuby = /*@__PURE__*/ createMotionComponent("ruby");
const MotionS = /*@__PURE__*/ createMotionComponent("s");
const MotionSamp = /*@__PURE__*/ createMotionComponent("samp");
const MotionScript = /*@__PURE__*/ createMotionComponent("script");
const MotionSection = /*@__PURE__*/ createMotionComponent("section");
const MotionSelect = /*@__PURE__*/ createMotionComponent("select");
const MotionSmall = /*@__PURE__*/ createMotionComponent("small");
const MotionSource = /*@__PURE__*/ createMotionComponent("source");
const MotionSpan = /*@__PURE__*/ createMotionComponent("span");
const MotionStrong = /*@__PURE__*/ createMotionComponent("strong");
const MotionStyle = /*@__PURE__*/ createMotionComponent("style");
const MotionSub = /*@__PURE__*/ createMotionComponent("sub");
const MotionSummary = /*@__PURE__*/ createMotionComponent("summary");
const MotionSup = /*@__PURE__*/ createMotionComponent("sup");
const MotionTable = /*@__PURE__*/ createMotionComponent("table");
const MotionTbody = /*@__PURE__*/ createMotionComponent("tbody");
const MotionTd = /*@__PURE__*/ createMotionComponent("td");
const MotionTextarea = /*@__PURE__*/ createMotionComponent("textarea");
const MotionTfoot = /*@__PURE__*/ createMotionComponent("tfoot");
const MotionTh = /*@__PURE__*/ createMotionComponent("th");
const MotionThead = /*@__PURE__*/ createMotionComponent("thead");
const MotionTime = /*@__PURE__*/ createMotionComponent("time");
const MotionTitle = /*@__PURE__*/ createMotionComponent("title");
const MotionTr = /*@__PURE__*/ createMotionComponent("tr");
const MotionTrack = /*@__PURE__*/ createMotionComponent("track");
const MotionU = /*@__PURE__*/ createMotionComponent("u");
const MotionUl = /*@__PURE__*/ createMotionComponent("ul");
const MotionVideo = /*@__PURE__*/ createMotionComponent("video");
const MotionWbr = /*@__PURE__*/ createMotionComponent("wbr");
const MotionWebview = /*@__PURE__*/ createMotionComponent("webview");
/**
 * SVG components
 */
const MotionAnimate = /*@__PURE__*/ createMotionComponent("animate");
const MotionCircle = /*@__PURE__*/ createMotionComponent("circle");
const MotionDefs = /*@__PURE__*/ createMotionComponent("defs");
const MotionDesc = /*@__PURE__*/ createMotionComponent("desc");
const MotionEllipse = /*@__PURE__*/ createMotionComponent("ellipse");
const MotionG = /*@__PURE__*/ createMotionComponent("g");
const MotionImage = /*@__PURE__*/ createMotionComponent("image");
const MotionLine = /*@__PURE__*/ createMotionComponent("line");
const MotionFilter = /*@__PURE__*/ createMotionComponent("filter");
const MotionMarker = /*@__PURE__*/ createMotionComponent("marker");
const MotionMask = /*@__PURE__*/ createMotionComponent("mask");
const MotionMetadata = /*@__PURE__*/ createMotionComponent("metadata");
const MotionPath = /*@__PURE__*/ createMotionComponent("path");
const MotionPattern = /*@__PURE__*/ createMotionComponent("pattern");
const MotionPolygon = /*@__PURE__*/ createMotionComponent("polygon");
const MotionPolyline = /*@__PURE__*/ createMotionComponent("polyline");
const MotionRect = /*@__PURE__*/ createMotionComponent("rect");
const MotionStop = /*@__PURE__*/ createMotionComponent("stop");
const MotionSvg = /*@__PURE__*/ createMotionComponent("svg");
const MotionSymbol = /*@__PURE__*/ createMotionComponent("symbol");
const MotionText = /*@__PURE__*/ createMotionComponent("text");
const MotionTspan = /*@__PURE__*/ createMotionComponent("tspan");
const MotionUse = /*@__PURE__*/ createMotionComponent("use");
const MotionView = /*@__PURE__*/ createMotionComponent("view");
const MotionClipPath = /*@__PURE__*/ createMotionComponent("clipPath");
const MotionFeBlend = /*@__PURE__*/ createMotionComponent("feBlend");
const MotionFeColorMatrix = 
/*@__PURE__*/ createMotionComponent("feColorMatrix");
const MotionFeComponentTransfer = /*@__PURE__*/ createMotionComponent("feComponentTransfer");
const MotionFeComposite = 
/*@__PURE__*/ createMotionComponent("feComposite");
const MotionFeConvolveMatrix = 
/*@__PURE__*/ createMotionComponent("feConvolveMatrix");
const MotionFeDiffuseLighting = 
/*@__PURE__*/ createMotionComponent("feDiffuseLighting");
const MotionFeDisplacementMap = 
/*@__PURE__*/ createMotionComponent("feDisplacementMap");
const MotionFeDistantLight = 
/*@__PURE__*/ createMotionComponent("feDistantLight");
const MotionFeDropShadow = 
/*@__PURE__*/ createMotionComponent("feDropShadow");
const MotionFeFlood = /*@__PURE__*/ createMotionComponent("feFlood");
const MotionFeFuncA = /*@__PURE__*/ createMotionComponent("feFuncA");
const MotionFeFuncB = /*@__PURE__*/ createMotionComponent("feFuncB");
const MotionFeFuncG = /*@__PURE__*/ createMotionComponent("feFuncG");
const MotionFeFuncR = /*@__PURE__*/ createMotionComponent("feFuncR");
const MotionFeGaussianBlur = 
/*@__PURE__*/ createMotionComponent("feGaussianBlur");
const MotionFeImage = /*@__PURE__*/ createMotionComponent("feImage");
const MotionFeMerge = /*@__PURE__*/ createMotionComponent("feMerge");
const MotionFeMergeNode = 
/*@__PURE__*/ createMotionComponent("feMergeNode");
const MotionFeMorphology = 
/*@__PURE__*/ createMotionComponent("feMorphology");
const MotionFeOffset = /*@__PURE__*/ createMotionComponent("feOffset");
const MotionFePointLight = 
/*@__PURE__*/ createMotionComponent("fePointLight");
const MotionFeSpecularLighting = 
/*@__PURE__*/ createMotionComponent("feSpecularLighting");
const MotionFeSpotLight = 
/*@__PURE__*/ createMotionComponent("feSpotLight");
const MotionFeTile = /*@__PURE__*/ createMotionComponent("feTile");
const MotionFeTurbulence = 
/*@__PURE__*/ createMotionComponent("feTurbulence");
const MotionForeignObject = 
/*@__PURE__*/ createMotionComponent("foreignObject");
const MotionLinearGradient = 
/*@__PURE__*/ createMotionComponent("linearGradient");
const MotionRadialGradient = 
/*@__PURE__*/ createMotionComponent("radialGradient");
const MotionTextPath = /*@__PURE__*/ createMotionComponent("textPath");

export { MotionA, MotionAbbr, MotionAddress, MotionAnimate, MotionArea, MotionArticle, MotionAside, MotionAudio, MotionB, MotionBase, MotionBdi, MotionBdo, MotionBig, MotionBlockquote, MotionBody, MotionButton, MotionCanvas, MotionCaption, MotionCircle, MotionCite, MotionClipPath, MotionCode, MotionCol, MotionColgroup, MotionData, MotionDatalist, MotionDd, MotionDefs, MotionDel, MotionDesc, MotionDetails, MotionDfn, MotionDialog, MotionDiv, MotionDl, MotionDt, MotionEllipse, MotionEm, MotionEmbed, MotionFeBlend, MotionFeColorMatrix, MotionFeComponentTransfer, MotionFeComposite, MotionFeConvolveMatrix, MotionFeDiffuseLighting, MotionFeDisplacementMap, MotionFeDistantLight, MotionFeDropShadow, MotionFeFlood, MotionFeFuncA, MotionFeFuncB, MotionFeFuncG, MotionFeFuncR, MotionFeGaussianBlur, MotionFeImage, MotionFeMerge, MotionFeMergeNode, MotionFeMorphology, MotionFeOffset, MotionFePointLight, MotionFeSpecularLighting, MotionFeSpotLight, MotionFeTile, MotionFeTurbulence, MotionFieldset, MotionFigcaption, MotionFigure, MotionFilter, MotionFooter, MotionForeignObject, MotionForm, MotionG, MotionH1, MotionH2, MotionH3, MotionH4, MotionH5, MotionH6, MotionHead, MotionHeader, MotionHgroup, MotionHr, MotionHtml, MotionI, MotionIframe, MotionImage, MotionImg, MotionInput, MotionIns, MotionKbd, MotionKeygen, MotionLabel, MotionLegend, MotionLi, MotionLine, MotionLinearGradient, MotionLink, MotionMain, MotionMap, MotionMark, MotionMarker, MotionMask, MotionMenu, MotionMenuitem, MotionMetadata, MotionMeter, MotionNav, MotionObject, MotionOl, MotionOptgroup, MotionOption, MotionOutput, MotionP, MotionParam, MotionPath, MotionPattern, MotionPicture, MotionPolygon, MotionPolyline, MotionPre, MotionProgress, MotionQ, MotionRadialGradient, MotionRect, MotionRp, MotionRt, MotionRuby, MotionS, MotionSamp, MotionScript, MotionSection, MotionSelect, MotionSmall, MotionSource, MotionSpan, MotionStop, MotionStrong, MotionStyle, MotionSub, MotionSummary, MotionSup, MotionSvg, MotionSymbol, MotionTable, MotionTbody, MotionTd, MotionText, MotionTextPath, MotionTextarea, MotionTfoot, MotionTh, MotionThead, MotionTime, MotionTitle, MotionTr, MotionTrack, MotionTspan, MotionU, MotionUl, MotionUse, MotionVideo, MotionView, MotionWbr, MotionWebview };
