'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'

const languages = [
  { code: 'en', name: 'EN', flag: '🇺🇸' },
  { code: 'de', name: 'DE', flag: '🇩🇪' },
  { code: 'fr', name: 'FR', flag: '🇫🇷' },
  { code: 'es', name: 'ES', flag: '🇪🇸' },
  { code: 'it', name: 'IT', flag: '🇮🇹' },
  { code: 'nl', name: 'NL', flag: '🇳🇱' },
]

export function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [currentLang, setCurrentLang] = useState('en')
  const [showLangDropdown, setShowLangDropdown] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'glass-nav py-3' 
          : 'bg-transparent py-6'
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="tennis-ball">🎾</div>
            <span className="text-xl font-bold font-inter-tight text-gradient">
              Tennis Booking Pro
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link 
              href="#features" 
              className="text-slate-700 hover:text-blue-600 transition-colors font-medium"
            >
              Features
            </Link>
            <Link 
              href="#pricing" 
              className="text-slate-700 hover:text-blue-600 transition-colors font-medium"
            >
              Pricing
            </Link>
            <Link 
              href="#demo" 
              className="text-slate-700 hover:text-blue-600 transition-colors font-medium"
            >
              Demo
            </Link>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Language Switcher */}
            <div className="relative">
              <button
                onClick={() => setShowLangDropdown(!showLangDropdown)}
                className="flex items-center space-x-1 px-3 py-2 rounded-lg glass-card text-sm font-medium transition-all hover:bg-white/10"
              >
                <span className="text-lg">🌐</span>
                <span>{languages.find(l => l.code === currentLang)?.name}</span>
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>

              {showLangDropdown && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  className="absolute top-full right-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 overflow-hidden min-w-[120px]"
                >
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => {
                        setCurrentLang(lang.code)
                        setShowLangDropdown(false)
                      }}
                      className="w-full flex items-center space-x-2 px-4 py-2 text-sm hover:bg-slate-50 transition-colors"
                    >
                      <span>{lang.flag}</span>
                      <span>{lang.name}</span>
                    </button>
                  ))}
                </motion.div>
              )}
            </div>

            {/* Login */}
            <Link 
              href="/login" 
              className="text-slate-700 hover:text-blue-600 transition-colors font-medium"
            >
              Login
            </Link>

            {/* CTA Button */}
            <Link 
              href="/signup" 
              className="btn-premium px-6 py-2.5 rounded-full text-white font-semibold text-sm"
            >
              Start Free Trial
            </Link>
          </div>
        </div>
      </div>
    </motion.nav>
  )
}