/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter_Tight%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter-tight%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22interTight%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter_Tight%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter-tight%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22interTight%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/Hero.tsx */ \"(rsc)/./src/components/landing/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/ProblemSection.tsx */ \"(rsc)/./src/components/landing/ProblemSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(rsc)/./src/components/layout/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRGlyayBLbGFwcHJvdGhcXERlc2t0b3BcXGRlbW9idWNodW5nXFx0ZW5uaXMtYm9va2luZy1kZW1vXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7e6b353f3d3f\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERpcmsgS2xhcHByb3RoXFxEZXNrdG9wXFxkZW1vYnVjaHVuZ1xcdGVubmlzLWJvb2tpbmctZGVtb1xcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2U2YjM1M2YzZDNmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_Tight_arguments_variable_font_inter_tight_subsets_latin_display_swap_variableName_interTight___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter_Tight\",\"arguments\":[{\"variable\":\"--font-inter-tight\",\"subsets\":[\"latin\"],\"display\":\"swap\"}],\"variableName\":\"interTight\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter_Tight\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter-tight\\\",\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"interTight\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_Tight_arguments_variable_font_inter_tight_subsets_latin_display_swap_variableName_interTight___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_Tight_arguments_variable_font_inter_tight_subsets_latin_display_swap_variableName_interTight___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Tennis Booking Pro - The Intelligent Tennis Club Platform\",\n    description: \"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions. 50% cheaper than competitors.\",\n    keywords: [\n        \"tennis booking\",\n        \"court reservation\",\n        \"club management\",\n        \"AI-powered\",\n        \"tennis platform\"\n    ],\n    authors: [\n        {\n            name: \"Tennis Booking Pro\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_Tight_arguments_variable_font_inter_tight_subsets_latin_display_swap_variableName_interTight___WEBPACK_IMPORTED_MODULE_3___default().variable)} font-inter antialiased bg-white text-slate-900 overflow-x-hidden`,\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_layout_Navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/Navigation */ \"(rsc)/./src/components/layout/Navigation.tsx\");\n/* harmony import */ var _components_landing_Hero__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/Hero */ \"(rsc)/./src/components/landing/Hero.tsx\");\n/* harmony import */ var _components_landing_ProblemSection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/ProblemSection */ \"(rsc)/./src/components/landing/ProblemSection.tsx\");\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Navigation__WEBPACK_IMPORTED_MODULE_1__.Navigation, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Hero__WEBPACK_IMPORTED_MODULE_2__.Hero, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_ProblemSection__WEBPACK_IMPORTED_MODULE_3__.ProblemSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-slate-100 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-slate-900 mb-4\",\n                            children: \"\\uD83D\\uDEA7 More Premium Sections Coming Soon! \\uD83D\\uDEA7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-slate-600\",\n                            children: \"We're building the solution showcase, interactive demo, pricing shock reveal, and more premium sections with stunning animations.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 p-6 bg-white rounded-xl shadow-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-500\",\n                                children: [\n                                    \"Preview: Hero section with premium animations ✅\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 62\n                                    }, this),\n                                    \"Preview: Problem section with scroll reveals ✅\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 25,\n                                        columnNumber: 61\n                                    }, this),\n                                    \"Next: Solution showcase with phone mockup animations\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 67\n                                    }, this),\n                                    \"Next: Pricing section with the €39 shock reveal\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 62\n                                    }, this),\n                                    \"Next: Interactive demo and social proof sections\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/landing/Hero.tsx":
/*!*****************************************!*\
  !*** ./src/components/landing/Hero.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hero: () => (/* binding */ Hero)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Hero = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\Hero.tsx",
"Hero",
);

/***/ }),

/***/ "(rsc)/./src/components/landing/ProblemSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/landing/ProblemSection.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ProblemSection: () => (/* binding */ ProblemSection)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ProblemSection = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ProblemSection() from the server but ProblemSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\landing\\ProblemSection.tsx",
"ProblemSection",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Navigation: () => (/* binding */ Navigation)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Navigation = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\demobuchung\\tennis-booking-demo\\src\\components\\layout\\Navigation.tsx",
"Navigation",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter_Tight%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter-tight%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22interTight%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter_Tight%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-inter-tight%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22interTight%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/Hero.tsx */ \"(ssr)/./src/components/landing/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/ProblemSection.tsx */ \"(ssr)/./src/components/landing/ProblemSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Navigation.tsx */ \"(ssr)/./src/components/layout/Navigation.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CProblemSection.tsx%22%2C%22ids%22%3A%5B%22ProblemSection%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDirk%20Klapproth%5C%5CDesktop%5C%5Cdemobuchung%5C%5Ctennis-booking-demo%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CNavigation.tsx%22%2C%22ids%22%3A%5B%22Navigation%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/landing/Hero.tsx":
/*!*****************************************!*\
  !*** ./src/components/landing/Hero.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ Hero auto */ \n\n\n\nconst stats = [\n    {\n        value: 2,\n        label: 'Second Booking',\n        suffix: 'sec'\n    },\n    {\n        value: 6,\n        label: 'Languages',\n        suffix: ''\n    },\n    {\n        value: 50,\n        label: 'Cheaper',\n        suffix: '%'\n    },\n    {\n        value: 99.9,\n        label: 'Uptime',\n        suffix: '%'\n    }\n];\nfunction Hero() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Hero.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Hero.useEffect\"], []);\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/arialviewcourt.jpg\",\n                            alt: \"Tennis court aerial view\",\n                            fill: true,\n                            className: \"object-cover\",\n                            priority: true,\n                            quality: 90\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-br from-blue-900/80 via-blue-600/60 to-emerald-500/40\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            children: [\n                                ...Array(20)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"absolute w-2 h-2 bg-yellow-400/30 rounded-full\",\n                                    style: {\n                                        left: `${Math.random() * 100}%`,\n                                        top: `${Math.random() * 100}%`\n                                    },\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -30,\n                                            0\n                                        ],\n                                        opacity: [\n                                            0.3,\n                                            0.8,\n                                            0.3\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3 + Math.random() * 2,\n                                        repeat: Infinity,\n                                        delay: Math.random() * 2\n                                    }\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"flex justify-center mb-6\",\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.5,\n                                type: \"spring\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl tennis-ball\",\n                                children: \"\\uD83C\\uDFBE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.h1, {\n                            className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.3\n                            },\n                            children: [\n                                \"The\",\n                                ' ',\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\",\n                                            children: \"Intelligent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: \"absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full\",\n                                            initial: {\n                                                scaleX: 0\n                                            },\n                                            animate: {\n                                                scaleX: 1\n                                            },\n                                            transition: {\n                                                duration: 0.8,\n                                                delay: 1.2\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                \"Tennis Club Platform\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.p, {\n                            className: \"text-xl md:text-2xl text-blue-100 mb-10 max-w-4xl mx-auto leading-relaxed\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.5\n                            },\n                            children: \"Join 500+ forward-thinking clubs using AI-powered booking, social member engagement, and global payment solutions.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"flex flex-col sm:flex-row items-center justify-center gap-4 mb-16\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.7\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    className: \"btn-premium px-8 py-4 rounded-full text-slate-900 font-bold text-lg min-w-[200px]\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        \"Start Free Trial\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2\",\n                                            children: \"→\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    className: \"flex items-center px-8 py-4 rounded-full border-2 border-white/30 text-white font-semibold text-lg backdrop-blur-sm hover:bg-white/10 transition-all min-w-[200px]\",\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    whileTap: {\n                                        scale: 0.95\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 mr-2\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Demo\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.9\n                            },\n                            children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    className: \"glass-card p-6 rounded-2xl text-center\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 1 + index * 0.1\n                                    },\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            className: \"text-3xl md:text-4xl font-bold text-yellow-400 mb-2\",\n                                            initial: {\n                                                scale: 0\n                                            },\n                                            animate: {\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: 1.2 + index * 0.1,\n                                                type: \"spring\"\n                                            },\n                                            children: [\n                                                stat.value,\n                                                stat.suffix\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-blue-100 font-medium\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, stat.label, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 1,\n                    delay: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"w-6 h-10 border-2 border-white/50 rounded-full flex justify-center\",\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"w-1 h-2 bg-white/70 rounded-full mt-2\",\n                        animate: {\n                            y: [\n                                0,\n                                16,\n                                0\n                            ]\n                        },\n                        transition: {\n                            duration: 2,\n                            repeat: Infinity\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\Hero.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/landing/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/landing/ProblemSection.tsx":
/*!***************************************************!*\
  !*** ./src/components/landing/ProblemSection.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProblemSection: () => (/* binding */ ProblemSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ProblemSection auto */ \n\n\n\n\nconst problems = [\n    {\n        icon: '📞',\n        title: 'Phone Booking Hell',\n        description: 'Members calling at all hours, double bookings, lost reservations, overwhelmed staff',\n        color: 'from-red-500 to-pink-500'\n    },\n    {\n        icon: '💸',\n        title: 'Revenue Leaks',\n        description: 'No-shows, manual tracking, missed peak opportunities, complex pricing setup',\n        color: 'from-orange-500 to-red-500'\n    },\n    {\n        icon: '😤',\n        title: 'Member Frustration',\n        description: 'Outdated systems driving members to competitors, poor mobile experience',\n        color: 'from-purple-500 to-pink-500'\n    }\n];\nfunction ProblemSection() {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_3__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        ref: ref,\n        className: \"py-20 bg-gradient-to-b from-slate-50 to-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-30\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-10 left-10 w-20 h-20 bg-red-200 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-20 w-32 h-32 bg-orange-200 rounded-full blur-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-20 left-1/3 w-24 h-24 bg-purple-200 rounded-full blur-xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mb-16\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            y: 0\n                        } : {\n                            opacity: 0,\n                            y: 30\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.h2, {\n                                className: \"text-3xl md:text-5xl font-bold text-slate-900 mb-6\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: isInView ? {\n                                    opacity: 1,\n                                    y: 0\n                                } : {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.1\n                                },\n                                children: [\n                                    \"Still Managing Your Club\",\n                                    ' ',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-orange-500\",\n                                        children: \"the Old Way?\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.p, {\n                                className: \"text-xl text-slate-600 max-w-3xl mx-auto\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: isInView ? {\n                                    opacity: 1,\n                                    y: 0\n                                } : {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.2\n                                },\n                                children: \"These outdated management headaches are costing you members and revenue every day\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                        children: problems.map((problem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                className: \"relative group\",\n                                initial: {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                animate: isInView ? {\n                                    opacity: 1,\n                                    y: 0\n                                } : {\n                                    opacity: 0,\n                                    y: 50\n                                },\n                                transition: {\n                                    duration: 0.8,\n                                    delay: 0.3 + index * 0.1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-slate-100 group-hover:border-slate-200 h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `w-16 h-16 rounded-full bg-gradient-to-r ${problem.color} flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`,\n                                            children: problem.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-slate-900 mb-4 group-hover:text-slate-700 transition-colors\",\n                                            children: problem.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600 leading-relaxed\",\n                                            children: problem.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `absolute inset-0 bg-gradient-to-r ${problem.color} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            }, problem.title, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"relative max-w-4xl mx-auto\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        animate: isInView ? {\n                            opacity: 1,\n                            scale: 1\n                        } : {\n                            opacity: 0,\n                            scale: 0.9\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative rounded-2xl overflow-hidden shadow-2xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: \"/receptiondesk.jpg\",\n                                    alt: \"Overwhelmed reception desk\",\n                                    width: 800,\n                                    height: 400,\n                                    className: \"w-full h-64 md:h-80 object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex items-end justify-center p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        className: \"text-center text-white\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            y: 0\n                                        } : {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        transition: {\n                                            duration: 0.8,\n                                            delay: 1.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold mb-2\",\n                                                children: '\"We used to spend 4 hours daily just managing bookings...\"'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm opacity-90\",\n                                                children: \"- Tennis Club Manager (Before Tennis Booking Pro)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute top-4 left-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        x: 0\n                                    } : {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 1.4\n                                    },\n                                    children: \"Double Bookings!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute top-4 right-4 bg-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        x: 0\n                                    } : {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 1.6\n                                    },\n                                    children: \"Lost Revenue\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                    className: \"absolute bottom-20 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: isInView ? {\n                                        opacity: 1,\n                                        y: 0\n                                    } : {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 1.8\n                                    },\n                                    children: \"Frustrated Members\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                        className: \"text-center mt-16\",\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: isInView ? {\n                            opacity: 1\n                        } : {\n                            opacity: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 2\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            className: \"inline-flex items-center text-blue-600 font-semibold text-lg\",\n                            animate: {\n                                y: [\n                                    0,\n                                    -5,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity\n                            },\n                            children: \"There's a better way ↓\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\landing\\\\ProblemSection.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/landing/ProblemSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Navigation.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/Navigation.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Navigation: () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ Navigation auto */ \n\n\n\nconst languages = [\n    {\n        code: 'en',\n        name: 'EN',\n        flag: '🇺🇸'\n    },\n    {\n        code: 'de',\n        name: 'DE',\n        flag: '🇩🇪'\n    },\n    {\n        code: 'fr',\n        name: 'FR',\n        flag: '🇫🇷'\n    },\n    {\n        code: 'es',\n        name: 'ES',\n        flag: '🇪🇸'\n    },\n    {\n        code: 'it',\n        name: 'IT',\n        flag: '🇮🇹'\n    },\n    {\n        code: 'nl',\n        name: 'NL',\n        flag: '🇳🇱'\n    }\n];\nfunction Navigation() {\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentLang, setCurrentLang] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [showLangDropdown, setShowLangDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Navigation.useEffect\": ()=>{\n            const handleScroll = {\n                \"Navigation.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"Navigation.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"Navigation.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"Navigation.useEffect\"];\n        }\n    }[\"Navigation.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.nav, {\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.8,\n            ease: \"easeOut\"\n        },\n        className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'glass-nav py-3' : 'bg-transparent py-6'}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"tennis-ball\",\n                                children: \"\\uD83C\\uDFBE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xl font-bold font-inter-tight text-gradient\",\n                                children: \"Tennis Booking Pro\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#features\",\n                                className: \"text-slate-700 hover:text-blue-600 transition-colors font-medium\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#pricing\",\n                                className: \"text-slate-700 hover:text-blue-600 transition-colors font-medium\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"#demo\",\n                                className: \"text-slate-700 hover:text-blue-600 transition-colors font-medium\",\n                                children: \"Demo\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLangDropdown(!showLangDropdown),\n                                        className: \"flex items-center space-x-1 px-3 py-2 rounded-lg glass-card text-sm font-medium transition-all hover:bg-white/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: \"\\uD83C\\uDF10\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: languages.find((l)=>l.code === currentLang)?.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M19 9l-7 7-7-7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    showLangDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        exit: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        className: \"absolute top-full right-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 overflow-hidden min-w-[120px]\",\n                                        children: languages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setCurrentLang(lang.code);\n                                                    setShowLangDropdown(false);\n                                                },\n                                                className: \"w-full flex items-center space-x-2 px-4 py-2 text-sm hover:bg-slate-50 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: lang.flag\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: lang.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, lang.code, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                className: \"text-slate-700 hover:text-blue-600 transition-colors font-medium\",\n                                children: \"Login\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/signup\",\n                                className: \"btn-premium px-6 py-2.5 rounded-full text-white font-semibold text-sm\",\n                                children: \"Start Free Trial\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\demobuchung\\\\tennis-booking-demo\\\\src\\\\components\\\\layout\\\\Navigation.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Navigation.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDirk%20Klapproth%5CDesktop%5Cdemobuchung%5Ctennis-booking-demo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();