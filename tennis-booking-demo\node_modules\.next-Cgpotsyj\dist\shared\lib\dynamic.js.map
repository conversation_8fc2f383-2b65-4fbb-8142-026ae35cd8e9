{"version": 3, "sources": ["../../../src/shared/lib/dynamic.tsx"], "sourcesContent": ["import React from 'react'\nimport Loadable from './loadable.shared-runtime'\n\nconst isServerSide = typeof window === 'undefined'\n\ntype ComponentModule<P = {}> = { default: React.ComponentType<P> }\n\nexport declare type LoaderComponent<P = {}> = Promise<\n  React.ComponentType<P> | ComponentModule<P>\n>\n\nexport declare type Loader<P = {}> =\n  | (() => LoaderComponent<P>)\n  | LoaderComponent<P>\n\nexport type LoaderMap = { [module: string]: () => Loader<any> }\n\nexport type LoadableGeneratedOptions = {\n  webpack?(): any\n  modules?(): LoaderMap\n}\n\nexport type DynamicOptionsLoadingProps = {\n  error?: Error | null\n  isLoading?: boolean\n  pastDelay?: boolean\n  retry?: () => void\n  timedOut?: boolean\n}\n\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule<P>(mod: React.ComponentType<P> | ComponentModule<P>) {\n  return { default: (mod as ComponentModule<P>)?.default || mod }\n}\n\nexport type DynamicOptions<P = {}> = LoadableGeneratedOptions & {\n  loading?: (loadingProps: DynamicOptionsLoadingProps) => React.ReactNode\n  loader?: Loader<P> | LoaderMap\n  loadableGenerated?: LoadableGeneratedOptions\n  ssr?: boolean\n}\n\nexport type LoadableOptions<P = {}> = DynamicOptions<P>\n\nexport type LoadableFn<P = {}> = (\n  opts: LoadableOptions<P>\n) => React.ComponentType<P>\n\nexport type LoadableComponent<P = {}> = React.ComponentType<P>\n\nexport function noSSR<P = {}>(\n  LoadableInitializer: LoadableFn<P>,\n  loadableOptions: DynamicOptions<P>\n): React.ComponentType<P> {\n  // Removing webpack and modules means react-loadable won't try preloading\n  delete loadableOptions.webpack\n  delete loadableOptions.modules\n\n  // This check is necessary to prevent react-loadable from initializing on the server\n  if (!isServerSide) {\n    return LoadableInitializer(loadableOptions)\n  }\n\n  const Loading = loadableOptions.loading!\n  // This will only be rendered on the server side\n  return () => (\n    <Loading error={null} isLoading pastDelay={false} timedOut={false} />\n  )\n}\n\n/**\n * This function lets you dynamically import a component.\n * It uses [React.lazy()](https://react.dev/reference/react/lazy) with [Suspense](https://react.dev/reference/react/Suspense) under the hood.\n *\n * Read more: [Next.js Docs: `next/dynamic`](https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading#nextdynamic)\n */\nexport default function dynamic<P = {}>(\n  dynamicOptions: DynamicOptions<P> | Loader<P>,\n  options?: DynamicOptions<P>\n): React.ComponentType<P> {\n  let loadableFn = Loadable as LoadableFn<P>\n\n  let loadableOptions: LoadableOptions<P> = {\n    // A loading component is not required, so we default it\n    loading: ({ error, isLoading, pastDelay }) => {\n      if (!pastDelay) return null\n      if (process.env.NODE_ENV !== 'production') {\n        if (isLoading) {\n          return null\n        }\n        if (error) {\n          return (\n            <p>\n              {error.message}\n              <br />\n              {error.stack}\n            </p>\n          )\n        }\n      }\n      return null\n    },\n  }\n\n  // Support for direct import(), eg: dynamic(import('../hello-world'))\n  // Note that this is only kept for the edge case where someone is passing in a promise as first argument\n  // The react-loadable babel plugin will turn dynamic(import('../hello-world')) into dynamic(() => import('../hello-world'))\n  // To make sure we don't execute the import without rendering first\n  if (dynamicOptions instanceof Promise) {\n    loadableOptions.loader = () => dynamicOptions\n    // Support for having import as a function, eg: dynamic(() => import('../hello-world'))\n  } else if (typeof dynamicOptions === 'function') {\n    loadableOptions.loader = dynamicOptions\n    // Support for having first argument being options, eg: dynamic({loader: import('../hello-world')})\n  } else if (typeof dynamicOptions === 'object') {\n    loadableOptions = { ...loadableOptions, ...dynamicOptions }\n  }\n\n  // Support for passing options, eg: dynamic(import('../hello-world'), {loading: () => <p>Loading something</p>})\n  loadableOptions = { ...loadableOptions, ...options }\n\n  const loaderFn = loadableOptions.loader as () => LoaderComponent<P>\n  const loader = () =>\n    loaderFn != null\n      ? loaderFn().then(convertModule)\n      : Promise.resolve(convertModule(() => null))\n\n  // coming from build/babel/plugins/react-loadable-plugin.js\n  if (loadableOptions.loadableGenerated) {\n    loadableOptions = {\n      ...loadableOptions,\n      ...loadableOptions.loadableGenerated,\n    }\n    delete loadableOptions.loadableGenerated\n  }\n\n  // support for disabling server side rendering, eg: dynamic(() => import('../hello-world'), {ssr: false}).\n  if (typeof loadableOptions.ssr === 'boolean' && !loadableOptions.ssr) {\n    delete loadableOptions.webpack\n    delete loadableOptions.modules\n\n    return noSSR(loadableFn, loadableOptions)\n  }\n\n  return loadableFn({ ...loadableOptions, loader: loader as Loader<P> })\n}\n"], "names": ["dynamic", "noSSR", "isServerSide", "window", "convertModule", "mod", "default", "LoadableInitializer", "loadableOptions", "webpack", "modules", "Loading", "loading", "error", "isLoading", "past<PERSON>elay", "timedOut", "dynamicOptions", "options", "loadableFn", "Loadable", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "Promise", "loader", "loaderFn", "then", "resolve", "loadableGenerated", "ssr"], "mappings": ";;;;;;;;;;;;;;;IAwEA;;;;;CAKC,GACD,OAqEC;eArEuBA;;IA1BRC,KAAK;eAALA;;;;;gEApDE;gFACG;AAErB,MAAMC,eAAe,OAAOC,WAAW;AA2BvC,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cAAiBC,GAAgD;IACxE,OAAO;QAAEC,SAAS,CAACD,uBAAD,AAACA,IAA4BC,OAAO,KAAID;IAAI;AAChE;AAiBO,SAASJ,MACdM,mBAAkC,EAClCC,eAAkC;IAElC,yEAAyE;IACzE,OAAOA,gBAAgBC,OAAO;IAC9B,OAAOD,gBAAgBE,OAAO;IAE9B,oFAAoF;IACpF,IAAI,CAACR,cAAc;QACjB,OAAOK,oBAAoBC;IAC7B;IAEA,MAAMG,UAAUH,gBAAgBI,OAAO;IACvC,gDAAgD;IAChD,OAAO,kBACL,qBAACD;YAAQE,OAAO;YAAMC,SAAS;YAACC,WAAW;YAAOC,UAAU;;AAEhE;AAQe,SAAShB,QACtBiB,cAA6C,EAC7CC,OAA2B;IAE3B,IAAIC,aAAaC,8BAAQ;IAEzB,IAAIZ,kBAAsC;QACxC,wDAAwD;QACxDI,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIM,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIT,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,sBAACW;;4BACEX,MAAMY,OAAO;0CACd,qBAACC;4BACAb,MAAMc,KAAK;;;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,qEAAqE;IACrE,wGAAwG;IACxG,2HAA2H;IAC3H,mEAAmE;IACnE,IAAIV,0BAA0BW,SAAS;QACrCpB,gBAAgBqB,MAAM,GAAG,IAAMZ;IAC/B,uFAAuF;IACzF,OAAO,IAAI,OAAOA,mBAAmB,YAAY;QAC/CT,gBAAgBqB,MAAM,GAAGZ;IACzB,mGAAmG;IACrG,OAAO,IAAI,OAAOA,mBAAmB,UAAU;QAC7CT,kBAAkB;YAAE,GAAGA,eAAe;YAAE,GAAGS,cAAc;QAAC;IAC5D;IAEA,gHAAgH;IAChHT,kBAAkB;QAAE,GAAGA,eAAe;QAAE,GAAGU,OAAO;IAAC;IAEnD,MAAMY,WAAWtB,gBAAgBqB,MAAM;IACvC,MAAMA,SAAS,IACbC,YAAY,OACRA,WAAWC,IAAI,CAAC3B,iBAChBwB,QAAQI,OAAO,CAAC5B,cAAc,IAAM;IAE1C,2DAA2D;IAC3D,IAAII,gBAAgByB,iBAAiB,EAAE;QACrCzB,kBAAkB;YAChB,GAAGA,eAAe;YAClB,GAAGA,gBAAgByB,iBAAiB;QACtC;QACA,OAAOzB,gBAAgByB,iBAAiB;IAC1C;IAEA,0GAA0G;IAC1G,IAAI,OAAOzB,gBAAgB0B,GAAG,KAAK,aAAa,CAAC1B,gBAAgB0B,GAAG,EAAE;QACpE,OAAO1B,gBAAgBC,OAAO;QAC9B,OAAOD,gBAAgBE,OAAO;QAE9B,OAAOT,MAAMkB,YAAYX;IAC3B;IAEA,OAAOW,WAAW;QAAE,GAAGX,eAAe;QAAEqB,QAAQA;IAAoB;AACtE"}