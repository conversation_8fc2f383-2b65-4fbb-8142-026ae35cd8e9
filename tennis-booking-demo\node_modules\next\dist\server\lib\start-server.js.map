{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "sourcesContent": ["import { getNetworkHost } from '../../lib/get-network-host'\n\nif (performance.getEntriesByName('next-start').length === 0) {\n  performance.mark('next-start')\n}\nimport '../next'\nimport '../require-hook'\n\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { SelfSignedCertificate } from '../../lib/mkcert'\nimport type { WorkerRequestHandler, WorkerUpgradeHandler } from './types'\n\nimport fs from 'fs'\nimport v8 from 'v8'\nimport path from 'path'\nimport http from 'http'\nimport https from 'https'\nimport os from 'os'\nimport Watchpack from 'next/dist/compiled/watchpack'\nimport * as Log from '../../build/output/log'\nimport setupDebug from 'next/dist/compiled/debug'\nimport {\n  RESTART_EXIT_CODE,\n  getFormattedDebugAddress,\n  getNodeDebugType,\n} from './utils'\nimport { formatHostname } from './format-hostname'\nimport { initialize } from './router-server'\nimport { CONFIG_FILES } from '../../shared/lib/constants'\nimport { getStartServerInfo, logStartInfo } from './app-info-log'\nimport { validateTurboNextConfig } from '../../lib/turbopack-warning'\nimport { type Span, trace, flushAllTraces } from '../../trace'\nimport { isPostpone } from './router-utils/is-postpone'\nimport { isIPv6 } from './is-ipv6'\nimport { AsyncCallbackSet } from './async-callback-set'\nimport type { NextServer } from '../next'\nimport type { ConfiguredExperimentalFeature } from '../config'\n\nconst debug = setupDebug('next:start-server')\nlet startServerSpan: Span | undefined\n\nexport interface StartServerOptions {\n  dir: string\n  port: number\n  isDev: boolean\n  hostname?: string\n  allowRetry?: boolean\n  customServer?: boolean\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  // this is dev-server only\n  selfSignedCertificate?: SelfSignedCertificate\n}\n\nexport async function getRequestHandlers({\n  dir,\n  port,\n  isDev,\n  onDevServerCleanup,\n  server,\n  hostname,\n  minimalMode,\n  keepAliveTimeout,\n  experimentalHttpsServer,\n  quiet,\n}: {\n  dir: string\n  port: number\n  isDev: boolean\n  onDevServerCleanup: ((listener: () => Promise<void>) => void) | undefined\n  server?: import('http').Server\n  hostname?: string\n  minimalMode?: boolean\n  keepAliveTimeout?: number\n  experimentalHttpsServer?: boolean\n  quiet?: boolean\n}): ReturnType<typeof initialize> {\n  return initialize({\n    dir,\n    port,\n    hostname,\n    onDevServerCleanup,\n    dev: isDev,\n    minimalMode,\n    server,\n    keepAliveTimeout,\n    experimentalHttpsServer,\n    startServerSpan,\n    quiet,\n  })\n}\n\nexport async function startServer(\n  serverOptions: StartServerOptions\n): Promise<void> {\n  const {\n    dir,\n    isDev,\n    hostname,\n    minimalMode,\n    allowRetry,\n    keepAliveTimeout,\n    selfSignedCertificate,\n  } = serverOptions\n  let { port } = serverOptions\n\n  process.title = `next-server (v${process.env.__NEXT_VERSION})`\n  let handlersReady = () => {}\n  let handlersError = () => {}\n\n  let handlersPromise: Promise<void> | undefined = new Promise<void>(\n    (resolve, reject) => {\n      handlersReady = resolve\n      handlersError = reject\n    }\n  )\n  let requestHandler: WorkerRequestHandler = async (\n    req: IncomingMessage,\n    res: ServerResponse\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return requestHandler(req, res)\n    }\n    throw new Error('Invariant request handler was not setup')\n  }\n  let upgradeHandler: WorkerUpgradeHandler = async (\n    req,\n    socket,\n    head\n  ): Promise<void> => {\n    if (handlersPromise) {\n      await handlersPromise\n      return upgradeHandler(req, socket, head)\n    }\n    throw new Error('Invariant upgrade handler was not setup')\n  }\n\n  let nextServer: NextServer | undefined\n\n  // setup server listener as fast as possible\n  if (selfSignedCertificate && !isDev) {\n    throw new Error(\n      'Using a self signed certificate is only supported with `next dev`.'\n    )\n  }\n\n  async function requestListener(req: IncomingMessage, res: ServerResponse) {\n    try {\n      if (handlersPromise) {\n        await handlersPromise\n        handlersPromise = undefined\n      }\n      await requestHandler(req, res)\n    } catch (err) {\n      res.statusCode = 500\n      res.end('Internal Server Error')\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    } finally {\n      if (isDev) {\n        if (\n          v8.getHeapStatistics().used_heap_size >\n          0.8 * v8.getHeapStatistics().heap_size_limit\n        ) {\n          Log.warn(\n            `Server is approaching the used memory threshold, restarting...`\n          )\n          trace('server-restart-close-to-memory-threshold', undefined, {\n            'memory.heapSizeLimit': String(\n              v8.getHeapStatistics().heap_size_limit\n            ),\n            'memory.heapUsed': String(v8.getHeapStatistics().used_heap_size),\n          }).stop()\n          await flushAllTraces()\n          process.exit(RESTART_EXIT_CODE)\n        }\n      }\n    }\n  }\n\n  const server = selfSignedCertificate\n    ? https.createServer(\n        {\n          key: fs.readFileSync(selfSignedCertificate.key),\n          cert: fs.readFileSync(selfSignedCertificate.cert),\n        },\n        requestListener\n      )\n    : http.createServer(requestListener)\n\n  if (keepAliveTimeout) {\n    server.keepAliveTimeout = keepAliveTimeout\n  }\n  server.on('upgrade', async (req, socket, head) => {\n    try {\n      await upgradeHandler(req, socket, head)\n    } catch (err) {\n      socket.destroy()\n      Log.error(`Failed to handle request for ${req.url}`)\n      console.error(err)\n    }\n  })\n\n  let portRetryCount = 0\n  const originalPort = port\n\n  server.on('error', (err: NodeJS.ErrnoException) => {\n    if (\n      allowRetry &&\n      port &&\n      isDev &&\n      err.code === 'EADDRINUSE' &&\n      portRetryCount < 10\n    ) {\n      port += 1\n      portRetryCount += 1\n      server.listen(port, hostname)\n    } else {\n      Log.error(`Failed to start server`)\n      console.error(err)\n      process.exit(1)\n    }\n  })\n\n  let cleanupListeners = isDev ? new AsyncCallbackSet() : undefined\n\n  await new Promise<void>((resolve) => {\n    server.on('listening', async () => {\n      const nodeDebugType = getNodeDebugType()\n\n      const addr = server.address()\n      const actualHostname = formatHostname(\n        typeof addr === 'object'\n          ? addr?.address || hostname || 'localhost'\n          : addr\n      )\n      const formattedHostname =\n        !hostname || actualHostname === '0.0.0.0'\n          ? 'localhost'\n          : actualHostname === '[::]'\n            ? '[::1]'\n            : formatHostname(hostname)\n\n      port = typeof addr === 'object' ? addr?.port || port : port\n\n      if (portRetryCount) {\n        Log.warn(\n          `Port ${originalPort} is in use, using available port ${port} instead.`\n        )\n      }\n\n      const networkHostname =\n        hostname ?? getNetworkHost(isIPv6(actualHostname) ? 'IPv6' : 'IPv4')\n\n      const protocol = selfSignedCertificate ? 'https' : 'http'\n\n      const networkUrl = networkHostname\n        ? `${protocol}://${formatHostname(networkHostname)}:${port}`\n        : null\n\n      const appUrl = `${protocol}://${formattedHostname}:${port}`\n\n      if (nodeDebugType) {\n        const formattedDebugAddress = getFormattedDebugAddress()\n        Log.info(\n          `the --${nodeDebugType} option was detected, the Next.js router server should be inspected at ${formattedDebugAddress}.`\n        )\n      }\n\n      // Store the selected port to:\n      // - expose it to render workers\n      // - re-use it for automatic dev server restarts with a randomly selected port\n      process.env.PORT = port + ''\n\n      process.env.__NEXT_PRIVATE_ORIGIN = appUrl\n\n      // Only load env and config in dev to for logging purposes\n      let envInfo: string[] | undefined\n      let experimentalFeatures: ConfiguredExperimentalFeature[] | undefined\n      if (isDev) {\n        const startServerInfo = await getStartServerInfo(dir, isDev)\n        envInfo = startServerInfo.envInfo\n        experimentalFeatures = startServerInfo.experimentalFeatures\n      }\n      logStartInfo({\n        networkUrl,\n        appUrl,\n        envInfo,\n        experimentalFeatures,\n        maxExperimentalFeatures: 3,\n      })\n\n      Log.event(`Starting...`)\n\n      try {\n        let cleanupStarted = false\n        let closeUpgraded: (() => void) | null = null\n        const cleanup = () => {\n          if (cleanupStarted) {\n            // We can get duplicate signals, e.g. when `ctrl+c` is used in an\n            // interactive shell (i.e. bash, zsh), the shell will recursively\n            // send SIGINT to children. The parent `next-dev` process will also\n            // send us SIGINT.\n            return\n          }\n          cleanupStarted = true\n          ;(async () => {\n            debug('start-server process cleanup')\n\n            // first, stop accepting new connections and finish pending requests,\n            // because they might affect `nextServer.close()` (e.g. by scheduling an `after`)\n            await new Promise<void>((res) => {\n              server.close((err) => {\n                if (err) console.error(err)\n                res()\n              })\n              if (isDev) {\n                server.closeAllConnections()\n                closeUpgraded?.()\n              }\n            })\n\n            // now that no new requests can come in, clean up the rest\n            await Promise.all([\n              nextServer?.close().catch(console.error),\n              cleanupListeners?.runAll().catch(console.error),\n            ])\n\n            debug('start-server process cleanup finished')\n            process.exit(0)\n          })()\n        }\n        const exception = (err: Error) => {\n          if (isPostpone(err)) {\n            // React postpones that are unhandled might end up logged here but they're\n            // not really errors. They're just part of rendering.\n            return\n          }\n\n          // This is the render worker, we keep the process alive\n          console.error(err)\n        }\n        // Make sure commands gracefully respect termination signals (e.g. from Docker)\n        // Allow the graceful termination to be manually configurable\n        if (!process.env.NEXT_MANUAL_SIG_HANDLE) {\n          process.on('SIGINT', cleanup)\n          process.on('SIGTERM', cleanup)\n        }\n        process.on('rejectionHandled', () => {\n          // It is ok to await a Promise late in Next.js as it allows for better\n          // prefetching patterns to avoid waterfalls. We ignore loggining these.\n          // We should've already errored in anyway unhandledRejection.\n        })\n        process.on('uncaughtException', exception)\n        process.on('unhandledRejection', exception)\n\n        const initResult = await getRequestHandlers({\n          dir,\n          port,\n          isDev,\n          onDevServerCleanup: cleanupListeners\n            ? cleanupListeners.add.bind(cleanupListeners)\n            : undefined,\n          server,\n          hostname,\n          minimalMode,\n          keepAliveTimeout,\n          experimentalHttpsServer: !!selfSignedCertificate,\n        })\n        requestHandler = initResult.requestHandler\n        upgradeHandler = initResult.upgradeHandler\n        nextServer = initResult.server\n        closeUpgraded = initResult.closeUpgraded\n\n        const startServerProcessDuration =\n          performance.mark('next-start-end') &&\n          performance.measure(\n            'next-start-duration',\n            'next-start',\n            'next-start-end'\n          ).duration\n\n        handlersReady()\n        const formatDurationText =\n          startServerProcessDuration > 2000\n            ? `${Math.round(startServerProcessDuration / 100) / 10}s`\n            : `${Math.round(startServerProcessDuration)}ms`\n\n        Log.event(`Ready in ${formatDurationText}`)\n\n        if (process.env.TURBOPACK) {\n          await validateTurboNextConfig({\n            dir: serverOptions.dir,\n            isDev: true,\n          })\n        }\n      } catch (err) {\n        // fatal error if we can't setup\n        handlersError()\n        console.error(err)\n        process.exit(1)\n      }\n\n      resolve()\n    })\n    server.listen(port, hostname)\n  })\n\n  if (isDev) {\n    function watchConfigFiles(\n      dirToWatch: string,\n      onChange: (filename: string) => void\n    ) {\n      const wp = new Watchpack()\n      wp.watch({\n        files: CONFIG_FILES.map((file) => path.join(dirToWatch, file)),\n      })\n      wp.on('change', onChange)\n    }\n    watchConfigFiles(dir, async (filename) => {\n      if (process.env.__NEXT_DISABLE_MEMORY_WATCHER) {\n        Log.info(\n          `Detected change, manual restart required due to '__NEXT_DISABLE_MEMORY_WATCHER' usage`\n        )\n        return\n      }\n\n      Log.warn(\n        `Found a change in ${path.basename(\n          filename\n        )}. Restarting the server to apply the changes...`\n      )\n      process.exit(RESTART_EXIT_CODE)\n    })\n  }\n}\n\nif (process.env.NEXT_PRIVATE_WORKER && process.send) {\n  process.addListener('message', async (msg: any) => {\n    if (\n      msg &&\n      typeof msg === 'object' &&\n      msg.nextWorkerOptions &&\n      process.send\n    ) {\n      startServerSpan = trace('start-dev-server', undefined, {\n        cpus: String(os.cpus().length),\n        platform: os.platform(),\n        'memory.freeMem': String(os.freemem()),\n        'memory.totalMem': String(os.totalmem()),\n        'memory.heapSizeLimit': String(v8.getHeapStatistics().heap_size_limit),\n      })\n      await startServerSpan.traceAsyncFn(() =>\n        startServer(msg.nextWorkerOptions)\n      )\n      const memoryUsage = process.memoryUsage()\n      startServerSpan.setAttribute('memory.rss', String(memoryUsage.rss))\n      startServerSpan.setAttribute(\n        'memory.heapTotal',\n        String(memoryUsage.heapTotal)\n      )\n      startServerSpan.setAttribute(\n        'memory.heapUsed',\n        String(memoryUsage.heapUsed)\n      )\n      process.send({ nextServerReady: true, port: process.env.PORT })\n    }\n  })\n  process.send({ nextWorkerReady: true })\n}\n"], "names": ["getRequestHandlers", "startServer", "performance", "getEntriesByName", "length", "mark", "debug", "setupDebug", "startServerSpan", "dir", "port", "isDev", "onDevServerCleanup", "server", "hostname", "minimalMode", "keepAliveTimeout", "experimentalHttpsServer", "quiet", "initialize", "dev", "serverOptions", "allowRetry", "selfSignedCertificate", "process", "title", "env", "__NEXT_VERSION", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "nextServer", "requestListener", "undefined", "err", "statusCode", "end", "Log", "error", "url", "console", "v8", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "trace", "String", "stop", "flushAllTraces", "exit", "RESTART_EXIT_CODE", "https", "createServer", "key", "fs", "readFileSync", "cert", "http", "on", "destroy", "portRetryCount", "originalPort", "code", "listen", "cleanupListeners", "AsyncCallbackSet", "nodeDebugType", "getNodeDebugType", "addr", "address", "actualHostname", "formatHostname", "formattedHostname", "networkHostname", "getNetworkHost", "isIPv6", "protocol", "networkUrl", "appUrl", "formattedDebugAddress", "getFormattedDebugAddress", "info", "PORT", "__NEXT_PRIVATE_ORIGIN", "envInfo", "experimentalFeatures", "startServerInfo", "getStartServerInfo", "logStartInfo", "maxExperimentalFeatures", "event", "cleanupStarted", "closeUpgraded", "cleanup", "close", "closeAllConnections", "all", "catch", "runAll", "exception", "isPostpone", "NEXT_MANUAL_SIG_HANDLE", "initResult", "add", "bind", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "TURBOPACK", "validateTurboNextConfig", "watchConfigFiles", "dirToWatch", "onChange", "wp", "Watchpack", "watch", "files", "CONFIG_FILES", "map", "file", "path", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "cpus", "os", "platform", "freemem", "totalmem", "traceAsyncFn", "memoryUsage", "setAttribute", "rss", "heapTotal", "heapUsed", "nextServerReady", "nextWorkerReady"], "mappings": ";;;;;;;;;;;;;;;IAsDsBA,kBAAkB;eAAlBA;;IAsCAC,WAAW;eAAXA;;;gCA5FS;QAKxB;QACA;2DAMQ;2DACA;6DACE;6DACA;8DACC;2DACH;kEACO;6DACD;8DACE;uBAKhB;gCACwB;8BACJ;2BACE;4BACoB;kCACT;uBACS;4BACtB;wBACJ;kCACU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAhCjC,IAAIC,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AAkCA,MAAMC,QAAQC,IAAAA,cAAU,EAAC;AACzB,IAAIC;AAeG,eAAeR,mBAAmB,EACvCS,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,kBAAkB,EAClBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,KAAK,EAYN;IACC,OAAOC,IAAAA,wBAAU,EAAC;QAChBV;QACAC;QACAI;QACAF;QACAQ,KAAKT;QACLI;QACAF;QACAG;QACAC;QACAT;QACAU;IACF;AACF;AAEO,eAAejB,YACpBoB,aAAiC;IAEjC,MAAM,EACJZ,GAAG,EACHE,KAAK,EACLG,QAAQ,EACRC,WAAW,EACXO,UAAU,EACVN,gBAAgB,EAChBO,qBAAqB,EACtB,GAAGF;IACJ,IAAI,EAAEX,IAAI,EAAE,GAAGW;IAEfG,QAAQC,KAAK,GAAG,CAAC,cAAc,EAAED,QAAQE,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC;IAC9D,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,qBAAoD,CAApD,IAAIC,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,qBAAoD,CAApD,IAAIH,MAAM,4CAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;IAEA,IAAII;IAEJ,4CAA4C;IAC5C,IAAIlB,yBAAyB,CAACZ,OAAO;QACnC,MAAM,qBAEL,CAFK,IAAI0B,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,eAAeK,gBAAgBP,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBa;YACpB;YACA,MAAMT,eAAeC,KAAKC;QAC5B,EAAE,OAAOQ,KAAK;YACZR,IAAIS,UAAU,GAAG;YACjBT,IAAIU,GAAG,CAAC;YACRC,KAAIC,KAAK,CAAC,CAAC,6BAA6B,EAAEb,IAAIc,GAAG,EAAE;YACnDC,QAAQF,KAAK,CAACJ;QAChB,SAAU;YACR,IAAIjC,OAAO;gBACT,IACEwC,WAAE,CAACC,iBAAiB,GAAGC,cAAc,GACrC,MAAMF,WAAE,CAACC,iBAAiB,GAAGE,eAAe,EAC5C;oBACAP,KAAIQ,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElEC,IAAAA,YAAK,EAAC,4CAA4Cb,WAAW;wBAC3D,wBAAwBc,OACtBN,WAAE,CAACC,iBAAiB,GAAGE,eAAe;wBAExC,mBAAmBG,OAAON,WAAE,CAACC,iBAAiB,GAAGC,cAAc;oBACjE,GAAGK,IAAI;oBACP,MAAMC,IAAAA,qBAAc;oBACpBnC,QAAQoC,IAAI,CAACC,wBAAiB;gBAChC;YACF;QACF;IACF;IAEA,MAAMhD,SAASU,wBACXuC,cAAK,CAACC,YAAY,CAChB;QACEC,KAAKC,WAAE,CAACC,YAAY,CAAC3C,sBAAsByC,GAAG;QAC9CG,MAAMF,WAAE,CAACC,YAAY,CAAC3C,sBAAsB4C,IAAI;IAClD,GACAzB,mBAEF0B,aAAI,CAACL,YAAY,CAACrB;IAEtB,IAAI1B,kBAAkB;QACpBH,OAAOG,gBAAgB,GAAGA;IAC5B;IACAH,OAAOwD,EAAE,CAAC,WAAW,OAAOlC,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOI,KAAK;YACZL,OAAO+B,OAAO;YACdvB,KAAIC,KAAK,CAAC,CAAC,6BAA6B,EAAEb,IAAIc,GAAG,EAAE;YACnDC,QAAQF,KAAK,CAACJ;QAChB;IACF;IAEA,IAAI2B,iBAAiB;IACrB,MAAMC,eAAe9D;IAErBG,OAAOwD,EAAE,CAAC,SAAS,CAACzB;QAClB,IACEtB,cACAZ,QACAC,SACAiC,IAAI6B,IAAI,KAAK,gBACbF,iBAAiB,IACjB;YACA7D,QAAQ;YACR6D,kBAAkB;YAClB1D,OAAO6D,MAAM,CAAChE,MAAMI;QACtB,OAAO;YACLiC,KAAIC,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACJ;YACdpB,QAAQoC,IAAI,CAAC;QACf;IACF;IAEA,IAAIe,mBAAmBhE,QAAQ,IAAIiE,kCAAgB,KAAKjC;IAExD,MAAM,IAAIZ,QAAc,CAACC;QACvBnB,OAAOwD,EAAE,CAAC,aAAa;YACrB,MAAMQ,gBAAgBC,IAAAA,uBAAgB;YAEtC,MAAMC,OAAOlE,OAAOmE,OAAO;YAC3B,MAAMC,iBAAiBC,IAAAA,8BAAc,EACnC,OAAOH,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAIlE,YAAY,cAC7BiE;YAEN,MAAMI,oBACJ,CAACrE,YAAYmE,mBAAmB,YAC5B,cACAA,mBAAmB,SACjB,UACAC,IAAAA,8BAAc,EAACpE;YAEvBJ,OAAO,OAAOqE,SAAS,WAAWA,CAAAA,wBAAAA,KAAMrE,IAAI,KAAIA,OAAOA;YAEvD,IAAI6D,gBAAgB;gBAClBxB,KAAIQ,IAAI,CACN,CAAC,KAAK,EAAEiB,aAAa,iCAAiC,EAAE9D,KAAK,SAAS,CAAC;YAE3E;YAEA,MAAM0E,kBACJtE,YAAYuE,IAAAA,8BAAc,EAACC,IAAAA,cAAM,EAACL,kBAAkB,SAAS;YAE/D,MAAMM,WAAWhE,wBAAwB,UAAU;YAEnD,MAAMiE,aAAaJ,kBACf,GAAGG,SAAS,GAAG,EAAEL,IAAAA,8BAAc,EAACE,iBAAiB,CAAC,EAAE1E,MAAM,GAC1D;YAEJ,MAAM+E,SAAS,GAAGF,SAAS,GAAG,EAAEJ,kBAAkB,CAAC,EAAEzE,MAAM;YAE3D,IAAImE,eAAe;gBACjB,MAAMa,wBAAwBC,IAAAA,+BAAwB;gBACtD5C,KAAI6C,IAAI,CACN,CAAC,MAAM,EAAEf,cAAc,uEAAuE,EAAEa,sBAAsB,CAAC,CAAC;YAE5H;YAEA,8BAA8B;YAC9B,gCAAgC;YAChC,8EAA8E;YAC9ElE,QAAQE,GAAG,CAACmE,IAAI,GAAGnF,OAAO;YAE1Bc,QAAQE,GAAG,CAACoE,qBAAqB,GAAGL;YAEpC,0DAA0D;YAC1D,IAAIM;YACJ,IAAIC;YACJ,IAAIrF,OAAO;gBACT,MAAMsF,kBAAkB,MAAMC,IAAAA,8BAAkB,EAACzF,KAAKE;gBACtDoF,UAAUE,gBAAgBF,OAAO;gBACjCC,uBAAuBC,gBAAgBD,oBAAoB;YAC7D;YACAG,IAAAA,wBAAY,EAAC;gBACXX;gBACAC;gBACAM;gBACAC;gBACAI,yBAAyB;YAC3B;YAEArD,KAAIsD,KAAK,CAAC,CAAC,WAAW,CAAC;YAEvB,IAAI;gBACF,IAAIC,iBAAiB;gBACrB,IAAIC,gBAAqC;gBACzC,MAAMC,UAAU;oBACd,IAAIF,gBAAgB;wBAClB,iEAAiE;wBACjE,iEAAiE;wBACjE,mEAAmE;wBACnE,kBAAkB;wBAClB;oBACF;oBACAA,iBAAiB;oBACf,CAAA;wBACAhG,MAAM;wBAEN,qEAAqE;wBACrE,iFAAiF;wBACjF,MAAM,IAAIyB,QAAc,CAACK;4BACvBvB,OAAO4F,KAAK,CAAC,CAAC7D;gCACZ,IAAIA,KAAKM,QAAQF,KAAK,CAACJ;gCACvBR;4BACF;4BACA,IAAIzB,OAAO;gCACTE,OAAO6F,mBAAmB;gCAC1BH,iCAAAA;4BACF;wBACF;wBAEA,0DAA0D;wBAC1D,MAAMxE,QAAQ4E,GAAG,CAAC;4BAChBlE,8BAAAA,WAAYgE,KAAK,GAAGG,KAAK,CAAC1D,QAAQF,KAAK;4BACvC2B,oCAAAA,iBAAkBkC,MAAM,GAAGD,KAAK,CAAC1D,QAAQF,KAAK;yBAC/C;wBAED1C,MAAM;wBACNkB,QAAQoC,IAAI,CAAC;oBACf,CAAA;gBACF;gBACA,MAAMkD,YAAY,CAAClE;oBACjB,IAAImE,IAAAA,sBAAU,EAACnE,MAAM;wBACnB,0EAA0E;wBAC1E,qDAAqD;wBACrD;oBACF;oBAEA,uDAAuD;oBACvDM,QAAQF,KAAK,CAACJ;gBAChB;gBACA,+EAA+E;gBAC/E,6DAA6D;gBAC7D,IAAI,CAACpB,QAAQE,GAAG,CAACsF,sBAAsB,EAAE;oBACvCxF,QAAQ6C,EAAE,CAAC,UAAUmC;oBACrBhF,QAAQ6C,EAAE,CAAC,WAAWmC;gBACxB;gBACAhF,QAAQ6C,EAAE,CAAC,oBAAoB;gBAC7B,sEAAsE;gBACtE,uEAAuE;gBACvE,6DAA6D;gBAC/D;gBACA7C,QAAQ6C,EAAE,CAAC,qBAAqByC;gBAChCtF,QAAQ6C,EAAE,CAAC,sBAAsByC;gBAEjC,MAAMG,aAAa,MAAMjH,mBAAmB;oBAC1CS;oBACAC;oBACAC;oBACAC,oBAAoB+D,mBAChBA,iBAAiBuC,GAAG,CAACC,IAAI,CAACxC,oBAC1BhC;oBACJ9B;oBACAC;oBACAC;oBACAC;oBACAC,yBAAyB,CAAC,CAACM;gBAC7B;gBACAW,iBAAiB+E,WAAW/E,cAAc;gBAC1CI,iBAAiB2E,WAAW3E,cAAc;gBAC1CG,aAAawE,WAAWpG,MAAM;gBAC9B0F,gBAAgBU,WAAWV,aAAa;gBAExC,MAAMa,6BACJlH,YAAYG,IAAI,CAAC,qBACjBH,YAAYmH,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZ1F;gBACA,MAAM2F,qBACJH,6BAA6B,OACzB,GAAGI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,GAAGI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnDrE,KAAIsD,KAAK,CAAC,CAAC,SAAS,EAAEkB,oBAAoB;gBAE1C,IAAI/F,QAAQE,GAAG,CAACgG,SAAS,EAAE;oBACzB,MAAMC,IAAAA,yCAAuB,EAAC;wBAC5BlH,KAAKY,cAAcZ,GAAG;wBACtBE,OAAO;oBACT;gBACF;YACF,EAAE,OAAOiC,KAAK;gBACZ,gCAAgC;gBAChCf;gBACAqB,QAAQF,KAAK,CAACJ;gBACdpB,QAAQoC,IAAI,CAAC;YACf;YAEA5B;QACF;QACAnB,OAAO6D,MAAM,CAAChE,MAAMI;IACtB;IAEA,IAAIH,OAAO;QACT,SAASiH,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAIC,kBAAS;YACxBD,GAAGE,KAAK,CAAC;gBACPC,OAAOC,uBAAY,CAACC,GAAG,CAAC,CAACC,OAASC,aAAI,CAACC,IAAI,CAACV,YAAYQ;YAC1D;YACAN,GAAG1D,EAAE,CAAC,UAAUyD;QAClB;QACAF,iBAAiBnH,KAAK,OAAO+H;YAC3B,IAAIhH,QAAQE,GAAG,CAAC+G,6BAA6B,EAAE;gBAC7C1F,KAAI6C,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEA7C,KAAIQ,IAAI,CACN,CAAC,kBAAkB,EAAE+E,aAAI,CAACI,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpDhH,QAAQoC,IAAI,CAACC,wBAAiB;QAChC;IACF;AACF;AAEA,IAAIrC,QAAQE,GAAG,CAACiH,mBAAmB,IAAInH,QAAQoH,IAAI,EAAE;IACnDpH,QAAQqH,WAAW,CAAC,WAAW,OAAOC;QACpC,IACEA,OACA,OAAOA,QAAQ,YACfA,IAAIC,iBAAiB,IACrBvH,QAAQoH,IAAI,EACZ;YACApI,kBAAkBgD,IAAAA,YAAK,EAAC,oBAAoBb,WAAW;gBACrDqG,MAAMvF,OAAOwF,WAAE,CAACD,IAAI,GAAG5I,MAAM;gBAC7B8I,UAAUD,WAAE,CAACC,QAAQ;gBACrB,kBAAkBzF,OAAOwF,WAAE,CAACE,OAAO;gBACnC,mBAAmB1F,OAAOwF,WAAE,CAACG,QAAQ;gBACrC,wBAAwB3F,OAAON,WAAE,CAACC,iBAAiB,GAAGE,eAAe;YACvE;YACA,MAAM9C,gBAAgB6I,YAAY,CAAC,IACjCpJ,YAAY6I,IAAIC,iBAAiB;YAEnC,MAAMO,cAAc9H,QAAQ8H,WAAW;YACvC9I,gBAAgB+I,YAAY,CAAC,cAAc9F,OAAO6F,YAAYE,GAAG;YACjEhJ,gBAAgB+I,YAAY,CAC1B,oBACA9F,OAAO6F,YAAYG,SAAS;YAE9BjJ,gBAAgB+I,YAAY,CAC1B,mBACA9F,OAAO6F,YAAYI,QAAQ;YAE7BlI,QAAQoH,IAAI,CAAC;gBAAEe,iBAAiB;gBAAMjJ,MAAMc,QAAQE,GAAG,CAACmE,IAAI;YAAC;QAC/D;IACF;IACArE,QAAQoH,IAAI,CAAC;QAAEgB,iBAAiB;IAAK;AACvC"}