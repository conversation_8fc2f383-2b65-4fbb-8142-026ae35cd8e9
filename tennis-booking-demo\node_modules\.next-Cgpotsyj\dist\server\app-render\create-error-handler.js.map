{"version": 3, "sources": ["../../../src/server/app-render/create-error-handler.tsx"], "sourcesContent": ["import type { ErrorInfo } from 'react'\n\nimport stringHash from 'next/dist/compiled/string-hash'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { SpanStatusCode, getTracer } from '../lib/trace/tracer'\nimport { isAbortError } from '../pipe-readable'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { getProperError } from '../../lib/is-error'\nimport { createDigestWithErrorCode } from '../../lib/error-telemetry-utils'\n\ndeclare global {\n  var __next_log_error__: undefined | ((err: unknown) => void)\n}\n\ntype RSCErrorHandler = (err: unknown) => string | undefined\ntype SSRErrorHandler = (\n  err: unknown,\n  errorInfo?: ErrorInfo\n) => string | undefined\n\nexport type DigestedError = Error & { digest: string }\n\n/**\n * Returns a digest for well-known Next.js errors, otherwise `undefined`. If a\n * digest is returned this also means that the error does not need to be\n * reported.\n */\nexport function getDigestForWellKnownError(error: unknown): string | undefined {\n  // If we're bailing out to CSR, we don't need to log the error.\n  if (isBailoutToCSRError(error)) return error.digest\n\n  // If this is a navigation error, we don't need to log the error.\n  if (isNextRouterError(error)) return error.digest\n\n  // If this error occurs, we know that we should be stopping the static\n  // render. This is only thrown in static generation when PPR is not enabled,\n  // which causes the whole page to be marked as dynamic. We don't need to\n  // tell the user about this error, as it's not actionable.\n  if (isDynamicServerError(error)) return error.digest\n\n  return undefined\n}\n\nexport function createFlightReactServerErrorHandler(\n  shouldFormatError: boolean,\n  onReactServerRenderError: (err: DigestedError) => void\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + err.stack || '').toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Record exception in an active span, if available.\n    const span = getTracer().getActiveScopeSpan()\n    if (span) {\n      span.recordException(err)\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      })\n    }\n\n    onReactServerRenderError(err)\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLReactServerErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  silenceLogger: boolean,\n  onReactServerRenderError: undefined | ((err: DigestedError) => void)\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + (err.stack || '')).toString()\n    }\n\n    // @TODO by putting this here and not at the top it is possible that\n    // we don't error the build in places we actually expect to\n    if (!reactServerErrors.has(err.digest)) {\n      reactServerErrors.set(err.digest, err)\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (!silenceLogger) {\n        onReactServerRenderError?.(err)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  allCapturedErrors: Array<unknown>,\n  silenceLogger: boolean,\n  onHTMLRenderSSRError: (err: DigestedError, errorInfo?: ErrorInfo) => void\n): SSRErrorHandler {\n  return (thrownValue: unknown, errorInfo?: ErrorInfo) => {\n    let isSSRError = true\n\n    allCapturedErrors.push(thrownValue)\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (err.digest) {\n      if (reactServerErrors.has(err.digest)) {\n        // This error is likely an obfuscated error from react-server.\n        // We recover the original error here.\n        thrownValue = reactServerErrors.get(err.digest)\n        isSSRError = false\n      } else {\n        // The error is not from react-server but has a digest\n        // from other means so we don't need to produce a new one\n      }\n    } else {\n      err.digest = stringHash(\n        err.message + (errorInfo?.componentStack || err.stack || '')\n      ).toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (\n        !silenceLogger &&\n        // HTML errors contain RSC errors as well, filter them out before reporting\n        isSSRError\n      ) {\n        onHTMLRenderSSRError(err, errorInfo)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function isUserLandError(err: any): boolean {\n  return (\n    !isAbortError(err) && !isBailoutToCSRError(err) && !isNextRouterError(err)\n  )\n}\n"], "names": ["createFlightReactServerErrorHandler", "createHTMLErrorHandler", "createHTMLReactServerErrorHandler", "getDigestForWellKnownError", "isUserLandError", "error", "isBailoutToCSRError", "digest", "isNextRouterError", "isDynamicServerError", "undefined", "shouldFormatError", "onReactServerRenderError", "thrownValue", "stringHash", "toString", "isAbortError", "err", "getProperError", "message", "stack", "formatServerError", "span", "getTracer", "getActiveScopeSpan", "recordException", "setStatus", "code", "SpanStatusCode", "ERROR", "createDigestWithErrorCode", "isNextExport", "reactServerErrors", "silenceLogger", "has", "set", "includes", "allCapturedErrors", "onHTMLRenderSSRError", "errorInfo", "isSSRError", "push", "get", "componentStack"], "mappings": ";;;;;;;;;;;;;;;;;;IA6CgBA,mCAAmC;eAAnCA;;IAuHAC,sBAAsB;eAAtBA;;IAtEAC,iCAAiC;eAAjCA;;IAjEAC,0BAA0B;eAA1BA;;IAqNAC,eAAe;eAAfA;;;mEAhPO;mCACW;wBACQ;8BACb;8BACO;oCACC;mCACH;yBACH;qCACW;;;;;;AAmBnC,SAASD,2BAA2BE,KAAc;IACvD,+DAA+D;IAC/D,IAAIC,IAAAA,iCAAmB,EAACD,QAAQ,OAAOA,MAAME,MAAM;IAEnD,iEAAiE;IACjE,IAAIC,IAAAA,oCAAiB,EAACH,QAAQ,OAAOA,MAAME,MAAM;IAEjD,sEAAsE;IACtE,4EAA4E;IAC5E,wEAAwE;IACxE,0DAA0D;IAC1D,IAAIE,IAAAA,wCAAoB,EAACJ,QAAQ,OAAOA,MAAME,MAAM;IAEpD,OAAOG;AACT;AAEO,SAASV,oCACdW,iBAA0B,EAC1BC,wBAAsD;IAEtD,OAAO,CAACC;QACN,IAAI,OAAOA,gBAAgB,UAAU;YACnC,+EAA+E;YAC/E,OAAOC,IAAAA,mBAAU,EAACD,aAAaE,QAAQ;QACzC;QAEA,8DAA8D;QAC9D,IAAIC,IAAAA,0BAAY,EAACH,cAAc;QAE/B,MAAMN,SAASJ,2BAA2BU;QAE1C,IAAIN,QAAQ;YACV,OAAOA;QACT;QAEA,MAAMU,MAAMC,IAAAA,uBAAc,EAACL;QAE3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACI,IAAIV,MAAM,EAAE;YACf,+EAA+E;YAC/EU,IAAIV,MAAM,GAAGO,IAAAA,mBAAU,EAACG,IAAIE,OAAO,GAAGF,IAAIG,KAAK,IAAI,IAAIL,QAAQ;QACjE;QAEA,yEAAyE;QACzE,IAAIJ,mBAAmB;YACrBU,IAAAA,oCAAiB,EAACJ;QACpB;QAEA,oDAAoD;QACpD,MAAMK,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;QAC3C,IAAIF,MAAM;YACRA,KAAKG,eAAe,CAACR;YACrBK,KAAKI,SAAS,CAAC;gBACbC,MAAMC,sBAAc,CAACC,KAAK;gBAC1BV,SAASF,IAAIE,OAAO;YACtB;QACF;QAEAP,yBAAyBK;QAEzB,OAAOa,IAAAA,8CAAyB,EAACjB,aAAaI,IAAIV,MAAM;IAC1D;AACF;AAEO,SAASL,kCACdS,iBAA0B,EAC1BoB,YAAqB,EACrBC,iBAA6C,EAC7CC,aAAsB,EACtBrB,wBAAoE;IAEpE,OAAO,CAACC;YAuCFI;QAtCJ,IAAI,OAAOJ,gBAAgB,UAAU;YACnC,+EAA+E;YAC/E,OAAOC,IAAAA,mBAAU,EAACD,aAAaE,QAAQ;QACzC;QAEA,8DAA8D;QAC9D,IAAIC,IAAAA,0BAAY,EAACH,cAAc;QAE/B,MAAMN,SAASJ,2BAA2BU;QAE1C,IAAIN,QAAQ;YACV,OAAOA;QACT;QAEA,MAAMU,MAAMC,IAAAA,uBAAc,EAACL;QAE3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAI,CAACI,IAAIV,MAAM,EAAE;YACf,+EAA+E;YAC/EU,IAAIV,MAAM,GAAGO,IAAAA,mBAAU,EAACG,IAAIE,OAAO,GAAIF,CAAAA,IAAIG,KAAK,IAAI,EAAC,GAAIL,QAAQ;QACnE;QAEA,oEAAoE;QACpE,2DAA2D;QAC3D,IAAI,CAACiB,kBAAkBE,GAAG,CAACjB,IAAIV,MAAM,GAAG;YACtCyB,kBAAkBG,GAAG,CAAClB,IAAIV,MAAM,EAAEU;QACpC;QAEA,yEAAyE;QACzE,IAAIN,mBAAmB;YACrBU,IAAAA,oCAAiB,EAACJ;QACpB;QAEA,+CAA+C;QAC/C,IACE,CACEc,CAAAA,iBACAd,wBAAAA,eAAAA,IAAKE,OAAO,qBAAZF,aAAcmB,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMd,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAACR;gBACrBK,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BV,SAASF,IAAIE,OAAO;gBACtB;YACF;YAEA,IAAI,CAACc,eAAe;gBAClBrB,4CAAAA,yBAA2BK;YAC7B;QACF;QAEA,OAAOa,IAAAA,8CAAyB,EAACjB,aAAaI,IAAIV,MAAM;IAC1D;AACF;AAEO,SAASN,uBACdU,iBAA0B,EAC1BoB,YAAqB,EACrBC,iBAA6C,EAC7CK,iBAAiC,EACjCJ,aAAsB,EACtBK,oBAAyE;IAEzE,OAAO,CAACzB,aAAsB0B;YA0CxBtB;QAzCJ,IAAIuB,aAAa;QAEjBH,kBAAkBI,IAAI,CAAC5B;QAEvB,8DAA8D;QAC9D,IAAIG,IAAAA,0BAAY,EAACH,cAAc;QAE/B,MAAMN,SAASJ,2BAA2BU;QAE1C,IAAIN,QAAQ;YACV,OAAOA;QACT;QAEA,MAAMU,MAAMC,IAAAA,uBAAc,EAACL;QAC3B,kEAAkE;QAClE,uDAAuD;QACvD,IAAII,IAAIV,MAAM,EAAE;YACd,IAAIyB,kBAAkBE,GAAG,CAACjB,IAAIV,MAAM,GAAG;gBACrC,8DAA8D;gBAC9D,sCAAsC;gBACtCM,cAAcmB,kBAAkBU,GAAG,CAACzB,IAAIV,MAAM;gBAC9CiC,aAAa;YACf,OAAO;YACL,sDAAsD;YACtD,yDAAyD;YAC3D;QACF,OAAO;YACLvB,IAAIV,MAAM,GAAGO,IAAAA,mBAAU,EACrBG,IAAIE,OAAO,GAAIoB,CAAAA,CAAAA,6BAAAA,UAAWI,cAAc,KAAI1B,IAAIG,KAAK,IAAI,EAAC,GAC1DL,QAAQ;QACZ;QAEA,yEAAyE;QACzE,IAAIJ,mBAAmB;YACrBU,IAAAA,oCAAiB,EAACJ;QACpB;QAEA,+CAA+C;QAC/C,IACE,CACEc,CAAAA,iBACAd,wBAAAA,eAAAA,IAAKE,OAAO,qBAAZF,aAAcmB,QAAQ,CACpB,4FACF,GAEF;YACA,oDAAoD;YACpD,MAAMd,OAAOC,IAAAA,iBAAS,IAAGC,kBAAkB;YAC3C,IAAIF,MAAM;gBACRA,KAAKG,eAAe,CAACR;gBACrBK,KAAKI,SAAS,CAAC;oBACbC,MAAMC,sBAAc,CAACC,KAAK;oBAC1BV,SAASF,IAAIE,OAAO;gBACtB;YACF;YAEA,IACE,CAACc,iBACD,2EAA2E;YAC3EO,YACA;gBACAF,qBAAqBrB,KAAKsB;YAC5B;QACF;QAEA,OAAOT,IAAAA,8CAAyB,EAACjB,aAAaI,IAAIV,MAAM;IAC1D;AACF;AAEO,SAASH,gBAAgBa,GAAQ;IACtC,OACE,CAACD,IAAAA,0BAAY,EAACC,QAAQ,CAACX,IAAAA,iCAAmB,EAACW,QAAQ,CAACT,IAAAA,oCAAiB,EAACS;AAE1E"}