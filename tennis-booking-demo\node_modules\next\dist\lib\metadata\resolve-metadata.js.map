{"version": 3, "sources": ["../../../src/lib/metadata/resolve-metadata.ts"], "sourcesContent": ["import type {\n  Metada<PERSON>,\n  ResolvedMetadata,\n  ResolvedViewport,\n  ResolvingMetadata,\n  ResolvingViewport,\n  Viewport,\n} from './types/metadata-interface'\nimport type { MetadataImageModule } from '../../build/webpack/loaders/metadata/types'\nimport type { GetDynamicParamFromSegment } from '../../server/app-render/app-render'\nimport type { Twitter } from './types/twitter-types'\nimport type { OpenGraph } from './types/opengraph-types'\nimport type { AppDirModules } from '../../build/webpack/loaders/next-app-loader'\nimport type { MetadataContext } from './types/resolvers'\nimport type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type {\n  AbsoluteTemplateString,\n  IconDescriptor,\n  ResolvedIcons,\n} from './types/metadata-types'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { StaticMetadata } from './types/icons'\nimport type { WorkStore } from '../../server/app-render/work-async-storage.external'\nimport type { Params } from '../../server/request/params'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport 'server-only'\n\nimport { cache } from 'react'\nimport {\n  createDefaultMetadata,\n  createDefaultViewport,\n} from './default-metadata'\nimport { resolveOpenGraph, resolveTwitter } from './resolvers/resolve-opengraph'\nimport { resolveTitle } from './resolvers/resolve-title'\nimport { resolveAsArrayOrUndefined } from './generate/utils'\nimport {\n  getComponentTypeModule,\n  getLayoutOrPageModule,\n} from '../../server/lib/app-dir-module'\nimport { interopDefault } from '../interop-default'\nimport {\n  resolveAlternates,\n  resolveAppleWebApp,\n  resolveAppLinks,\n  resolveRobots,\n  resolveThemeColor,\n  resolveVerification,\n  resolveItunes,\n  resolveFacebook,\n  resolvePagination,\n} from './resolvers/resolve-basics'\nimport { resolveIcons } from './resolvers/resolve-icons'\nimport { getTracer } from '../../server/lib/trace/tracer'\nimport { ResolveMetadataSpan } from '../../server/lib/trace/constants'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport * as Log from '../../build/output/log'\nimport { createServerParamsForMetadata } from '../../server/request/params'\n\ntype StaticIcons = Pick<ResolvedIcons, 'icon' | 'apple'>\n\ntype MetadataResolver = (\n  parent: ResolvingMetadata\n) => Metadata | Promise<Metadata>\ntype ViewportResolver = (\n  parent: ResolvingViewport\n) => Viewport | Promise<Viewport>\n\nexport type MetadataErrorType = 'not-found' | 'forbidden' | 'unauthorized'\n\nexport type MetadataItems = Array<\n  [Metadata | MetadataResolver | null, StaticMetadata]\n>\n\nexport type ViewportItems = Array<Viewport | ViewportResolver | null>\n\ntype TitleTemplates = {\n  title: string | null\n  twitter: string | null\n  openGraph: string | null\n}\n\ntype BuildState = {\n  warnings: Set<string>\n}\n\ntype LayoutProps = {\n  params: { [key: string]: any }\n}\ntype PageProps = {\n  params: { [key: string]: any }\n  searchParams: { [key: string]: any }\n}\n\nfunction isFavicon(icon: IconDescriptor | undefined): boolean {\n  if (!icon) {\n    return false\n  }\n\n  // turbopack appends a hash to all images\n  return (\n    (icon.url === '/favicon.ico' ||\n      icon.url.toString().startsWith('/favicon.ico?')) &&\n    icon.type === 'image/x-icon'\n  )\n}\n\nfunction mergeStaticMetadata(\n  source: Metadata | null,\n  target: ResolvedMetadata,\n  staticFilesMetadata: StaticMetadata,\n  metadataContext: MetadataContext,\n  titleTemplates: TitleTemplates,\n  leafSegmentStaticIcons: StaticIcons\n) {\n  if (!staticFilesMetadata) return\n  const { icon, apple, openGraph, twitter, manifest } = staticFilesMetadata\n\n  // Keep updating the static icons in the most leaf node\n\n  if (icon) {\n    leafSegmentStaticIcons.icon = icon\n  }\n  if (apple) {\n    leafSegmentStaticIcons.apple = apple\n  }\n\n  // file based metadata is specified and current level metadata twitter.images is not specified\n  if (twitter && !source?.twitter?.hasOwnProperty('images')) {\n    const resolvedTwitter = resolveTwitter(\n      { ...target.twitter, images: twitter } as Twitter,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.twitter\n    )\n    target.twitter = resolvedTwitter\n  }\n\n  // file based metadata is specified and current level metadata openGraph.images is not specified\n  if (openGraph && !source?.openGraph?.hasOwnProperty('images')) {\n    const resolvedOpenGraph = resolveOpenGraph(\n      { ...target.openGraph, images: openGraph } as OpenGraph,\n      target.metadataBase,\n      { ...metadataContext, isStaticMetadataRouteFile: true },\n      titleTemplates.openGraph\n    )\n    target.openGraph = resolvedOpenGraph\n  }\n  if (manifest) {\n    target.manifest = manifest\n  }\n\n  return target\n}\n\n// Merge the source metadata into the resolved target metadata.\nfunction mergeMetadata({\n  source,\n  target,\n  staticFilesMetadata,\n  titleTemplates,\n  metadataContext,\n  buildState,\n  leafSegmentStaticIcons,\n}: {\n  source: Metadata | null\n  target: ResolvedMetadata\n  staticFilesMetadata: StaticMetadata\n  titleTemplates: TitleTemplates\n  metadataContext: MetadataContext\n  buildState: BuildState\n  leafSegmentStaticIcons: StaticIcons\n}): void {\n  // If there's override metadata, prefer it otherwise fallback to the default metadata.\n  const metadataBase =\n    typeof source?.metadataBase !== 'undefined'\n      ? source.metadataBase\n      : target.metadataBase\n  for (const key_ in source) {\n    const key = key_ as keyof Metadata\n\n    switch (key) {\n      case 'title': {\n        target.title = resolveTitle(source.title, titleTemplates.title)\n        break\n      }\n      case 'alternates': {\n        target.alternates = resolveAlternates(\n          source.alternates,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      case 'openGraph': {\n        target.openGraph = resolveOpenGraph(\n          source.openGraph,\n          metadataBase,\n          metadataContext,\n          titleTemplates.openGraph\n        )\n        break\n      }\n      case 'twitter': {\n        target.twitter = resolveTwitter(\n          source.twitter,\n          metadataBase,\n          metadataContext,\n          titleTemplates.twitter\n        )\n        break\n      }\n      case 'facebook':\n        target.facebook = resolveFacebook(source.facebook)\n        break\n      case 'verification':\n        target.verification = resolveVerification(source.verification)\n        break\n\n      case 'icons': {\n        target.icons = resolveIcons(source.icons)\n        break\n      }\n      case 'appleWebApp':\n        target.appleWebApp = resolveAppleWebApp(source.appleWebApp)\n        break\n      case 'appLinks':\n        target.appLinks = resolveAppLinks(source.appLinks)\n        break\n      case 'robots': {\n        target.robots = resolveRobots(source.robots)\n        break\n      }\n      case 'archives':\n      case 'assets':\n      case 'bookmarks':\n      case 'keywords': {\n        target[key] = resolveAsArrayOrUndefined(source[key])\n        break\n      }\n      case 'authors': {\n        target[key] = resolveAsArrayOrUndefined(source.authors)\n        break\n      }\n      case 'itunes': {\n        target[key] = resolveItunes(\n          source.itunes,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      case 'pagination': {\n        target.pagination = resolvePagination(\n          source.pagination,\n          metadataBase,\n          metadataContext\n        )\n        break\n      }\n      // directly assign fields that fallback to null\n      case 'applicationName':\n      case 'description':\n      case 'generator':\n      case 'creator':\n      case 'publisher':\n      case 'category':\n      case 'classification':\n      case 'referrer':\n      case 'formatDetection':\n      case 'manifest':\n      case 'pinterest':\n        // @ts-ignore TODO: support inferring\n        target[key] = source[key] || null\n        break\n      case 'other':\n        target.other = Object.assign({}, target.other, source.other)\n        break\n      case 'metadataBase':\n        target.metadataBase = metadataBase\n        break\n\n      default: {\n        if (\n          (key === 'viewport' ||\n            key === 'themeColor' ||\n            key === 'colorScheme') &&\n          source[key] != null\n        ) {\n          buildState.warnings.add(\n            `Unsupported metadata ${key} is configured in metadata export in ${metadataContext.pathname}. Please move it to viewport export instead.\\nRead more: https://nextjs.org/docs/app/api-reference/functions/generate-viewport`\n          )\n        }\n        break\n      }\n    }\n  }\n  mergeStaticMetadata(\n    source,\n    target,\n    staticFilesMetadata,\n    metadataContext,\n    titleTemplates,\n    leafSegmentStaticIcons\n  )\n}\n\nfunction mergeViewport({\n  target,\n  source,\n}: {\n  target: ResolvedViewport\n  source: Viewport | null\n}): void {\n  if (!source) return\n  for (const key_ in source) {\n    const key = key_ as keyof Viewport\n\n    switch (key) {\n      case 'themeColor': {\n        target.themeColor = resolveThemeColor(source.themeColor)\n        break\n      }\n      case 'colorScheme':\n        target.colorScheme = source.colorScheme || null\n        break\n      default:\n        // always override the target with the source\n        // @ts-ignore viewport properties\n        target[key] = source[key]\n        break\n    }\n  }\n}\n\nfunction getDefinedViewport(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Viewport | ViewportResolver | null {\n  if (typeof mod.generateViewport === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingViewport) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateViewport,\n        {\n          spanName: `generateViewport ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateViewport(props, parent)\n      )\n  }\n  return mod.viewport || null\n}\n\nfunction getDefinedMetadata(\n  mod: any,\n  props: any,\n  tracingProps: { route: string }\n): Metadata | MetadataResolver | null {\n  if (typeof mod.generateMetadata === 'function') {\n    const { route } = tracingProps\n    return (parent: ResolvingMetadata) =>\n      getTracer().trace(\n        ResolveMetadataSpan.generateMetadata,\n        {\n          spanName: `generateMetadata ${route}`,\n          attributes: {\n            'next.page': route,\n          },\n        },\n        () => mod.generateMetadata(props, parent)\n      )\n  }\n  return mod.metadata || null\n}\n\nasync function collectStaticImagesFiles(\n  metadata: AppDirModules['metadata'],\n  props: any,\n  type: keyof NonNullable<AppDirModules['metadata']>\n) {\n  if (!metadata?.[type]) return undefined\n\n  const iconPromises = metadata[type as 'icon' | 'apple'].map(\n    async (imageModule: (p: any) => Promise<MetadataImageModule[]>) =>\n      interopDefault(await imageModule(props))\n  )\n\n  return iconPromises?.length > 0\n    ? (await Promise.all(iconPromises))?.flat()\n    : undefined\n}\n\nasync function resolveStaticMetadata(\n  modules: AppDirModules,\n  props: any\n): Promise<StaticMetadata> {\n  const { metadata } = modules\n  if (!metadata) return null\n\n  const [icon, apple, openGraph, twitter] = await Promise.all([\n    collectStaticImagesFiles(metadata, props, 'icon'),\n    collectStaticImagesFiles(metadata, props, 'apple'),\n    collectStaticImagesFiles(metadata, props, 'openGraph'),\n    collectStaticImagesFiles(metadata, props, 'twitter'),\n  ])\n\n  const staticMetadata = {\n    icon,\n    apple,\n    openGraph,\n    twitter,\n    manifest: metadata.manifest,\n  }\n\n  return staticMetadata\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectMetadata({\n  tree,\n  metadataItems,\n  errorMetadataItem,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  metadataItems: MetadataItems\n  errorMetadataItem: MetadataItems[number]\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const staticFilesMetadata = await resolveStaticMetadata(tree[2], props)\n  const metadataExport = mod ? getDefinedMetadata(mod, props, { route }) : null\n\n  metadataItems.push([metadataExport, staticFilesMetadata])\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorMetadataExport = errorMod\n      ? getDefinedMetadata(errorMod, props, { route })\n      : null\n\n    errorMetadataItem[0] = errorMetadataExport\n    errorMetadataItem[1] = staticFilesMetadata\n  }\n}\n\n// [layout.metadata, static files metadata] -> ... -> [page.metadata, static files metadata]\nasync function collectViewport({\n  tree,\n  viewportItems,\n  errorViewportItemRef,\n  props,\n  route,\n  errorConvention,\n}: {\n  tree: LoaderTree\n  viewportItems: ViewportItems\n  errorViewportItemRef: ErrorViewportItemRef\n  props: any\n  route: string\n  errorConvention?: MetadataErrorType\n}) {\n  let mod\n  let modType\n  const hasErrorConventionComponent = Boolean(\n    errorConvention && tree[2][errorConvention]\n  )\n  if (errorConvention) {\n    mod = await getComponentTypeModule(tree, 'layout')\n    modType = errorConvention\n  } else {\n    const { mod: layoutOrPageMod, modType: layoutOrPageModType } =\n      await getLayoutOrPageModule(tree)\n    mod = layoutOrPageMod\n    modType = layoutOrPageModType\n  }\n\n  if (modType) {\n    route += `/${modType}`\n  }\n\n  const viewportExport = mod ? getDefinedViewport(mod, props, { route }) : null\n\n  viewportItems.push(viewportExport)\n\n  if (hasErrorConventionComponent && errorConvention) {\n    const errorMod = await getComponentTypeModule(tree, errorConvention)\n    const errorViewportExport = errorMod\n      ? getDefinedViewport(errorMod, props, { route })\n      : null\n\n    errorViewportItemRef.current = errorViewportExport\n  }\n}\n\nconst resolveMetadataItems = cache(async function (\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const metadataItems: MetadataItems = []\n  const errorMetadataItem: MetadataItems[number] = [null, null]\n  const treePrefix = undefined\n  return resolveMetadataItemsImpl(\n    metadataItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorMetadataItem,\n    getDynamicParamFromSegment,\n    workStore\n  )\n})\n\nasync function resolveMetadataItemsImpl(\n  metadataItems: MetadataItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorMetadataItem: MetadataItems[number],\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<MetadataItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectMetadata({\n    tree,\n    metadataItems,\n    errorMetadataItem,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveMetadataItemsImpl(\n      metadataItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorMetadataItem,\n      getDynamicParamFromSegment,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    metadataItems.push(errorMetadataItem)\n  }\n\n  return metadataItems\n}\n\ntype ErrorViewportItemRef = { current: ViewportItems[number] }\nconst resolveViewportItems = cache(async function (\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n) {\n  const parentParams = {}\n  const viewportItems: ViewportItems = []\n  const errorViewportItemRef: ErrorViewportItemRef = {\n    current: null,\n  }\n  const treePrefix = undefined\n  return resolveViewportItemsImpl(\n    viewportItems,\n    tree,\n    treePrefix,\n    parentParams,\n    searchParams,\n    errorConvention,\n    errorViewportItemRef,\n    getDynamicParamFromSegment,\n    workStore\n  )\n})\n\nasync function resolveViewportItemsImpl(\n  viewportItems: ViewportItems,\n  tree: LoaderTree,\n  /** Provided tree can be nested subtree, this argument says what is the path of such subtree */\n  treePrefix: undefined | string[],\n  parentParams: Params,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  errorViewportItemRef: ErrorViewportItemRef,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<ViewportItems> {\n  const [segment, parallelRoutes, { page }] = tree\n  const currentTreePrefix =\n    treePrefix && treePrefix.length ? [...treePrefix, segment] : [segment]\n  const isPage = typeof page !== 'undefined'\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n  /**\n   * Create object holding the parent params and current params\n   */\n  let currentParams = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const params = createServerParamsForMetadata(currentParams, workStore)\n\n  let layerProps: LayoutProps | PageProps\n  if (isPage) {\n    layerProps = {\n      params,\n      searchParams,\n    }\n  } else {\n    layerProps = {\n      params,\n    }\n  }\n\n  await collectViewport({\n    tree,\n    viewportItems,\n    errorViewportItemRef,\n    errorConvention,\n    props: layerProps,\n    route: currentTreePrefix\n      // __PAGE__ shouldn't be shown in a route\n      .filter((s) => s !== PAGE_SEGMENT_KEY)\n      .join('/'),\n  })\n\n  for (const key in parallelRoutes) {\n    const childTree = parallelRoutes[key]\n    await resolveViewportItemsImpl(\n      viewportItems,\n      childTree,\n      currentTreePrefix,\n      currentParams,\n      searchParams,\n      errorConvention,\n      errorViewportItemRef,\n      getDynamicParamFromSegment,\n      workStore\n    )\n  }\n\n  if (Object.keys(parallelRoutes).length === 0 && errorConvention) {\n    // If there are no parallel routes, place error metadata as the last item.\n    // e.g. layout -> layout -> not-found\n    viewportItems.push(errorViewportItemRef.current)\n  }\n\n  return viewportItems\n}\n\ntype WithTitle = { title?: AbsoluteTemplateString | null }\ntype WithDescription = { description?: string | null }\n\nconst isTitleTruthy = (title: AbsoluteTemplateString | null | undefined) =>\n  !!title?.absolute\nconst hasTitle = (metadata: WithTitle | null) => isTitleTruthy(metadata?.title)\n\nfunction inheritFromMetadata(\n  target: (WithTitle & WithDescription) | null,\n  metadata: ResolvedMetadata\n) {\n  if (target) {\n    if (!hasTitle(target) && hasTitle(metadata)) {\n      target.title = metadata.title\n    }\n    if (!target.description && metadata.description) {\n      target.description = metadata.description\n    }\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nconst commonOgKeys = ['title', 'description', 'images'] as const\nfunction postProcessMetadata(\n  metadata: ResolvedMetadata,\n  favicon: any,\n  titleTemplates: TitleTemplates,\n  metadataContext: MetadataContext\n): ResolvedMetadata {\n  const { openGraph, twitter } = metadata\n\n  if (openGraph) {\n    // If there's openGraph information but not configured in twitter,\n    // inherit them from openGraph metadata.\n    let autoFillProps: Partial<{\n      [Key in (typeof commonOgKeys)[number]]: NonNullable<\n        ResolvedMetadata['openGraph']\n      >[Key]\n    }> = {}\n    const hasTwTitle = hasTitle(twitter)\n    const hasTwDescription = twitter?.description\n    const hasTwImages = Boolean(\n      twitter?.hasOwnProperty('images') && twitter.images\n    )\n    if (!hasTwTitle) {\n      if (isTitleTruthy(openGraph.title)) {\n        autoFillProps.title = openGraph.title\n      } else if (metadata.title && isTitleTruthy(metadata.title)) {\n        autoFillProps.title = metadata.title\n      }\n    }\n    if (!hasTwDescription)\n      autoFillProps.description =\n        openGraph.description || metadata.description || undefined\n    if (!hasTwImages) autoFillProps.images = openGraph.images\n\n    if (Object.keys(autoFillProps).length > 0) {\n      const partialTwitter = resolveTwitter(\n        autoFillProps,\n        metadata.metadataBase,\n        metadataContext,\n        titleTemplates.twitter\n      )\n      if (metadata.twitter) {\n        metadata.twitter = Object.assign({}, metadata.twitter, {\n          ...(!hasTwTitle && { title: partialTwitter?.title }),\n          ...(!hasTwDescription && {\n            description: partialTwitter?.description,\n          }),\n          ...(!hasTwImages && { images: partialTwitter?.images }),\n        })\n      } else {\n        metadata.twitter = partialTwitter\n      }\n    }\n  }\n\n  // If there's no title and description configured in openGraph or twitter,\n  // use the title and description from metadata.\n  inheritFromMetadata(openGraph, metadata)\n  inheritFromMetadata(twitter, metadata)\n\n  if (favicon) {\n    if (!metadata.icons) {\n      metadata.icons = {\n        icon: [],\n        apple: [],\n      }\n    }\n\n    metadata.icons.icon.unshift(favicon)\n  }\n\n  return metadata\n}\n\ntype Result<T> = null | T | Promise<null | T> | PromiseLike<null | T>\n\nfunction prerenderMetadata(metadataItems: MetadataItems) {\n  // If the index is a function then it is a resolver and the next slot\n  // is the corresponding result. If the index is not a function it is the result\n  // itself.\n  const resolversAndResults: Array<\n    ((value: ResolvedMetadata) => void) | Result<Metadata>\n  > = []\n  for (let i = 0; i < metadataItems.length; i++) {\n    const metadataExport = metadataItems[i][0]\n    getResult(resolversAndResults, metadataExport)\n  }\n  return resolversAndResults\n}\n\nfunction prerenderViewport(viewportItems: ViewportItems) {\n  // If the index is a function then it is a resolver and the next slot\n  // is the corresponding result. If the index is not a function it is the result\n  // itself.\n  const resolversAndResults: Array<\n    ((value: ResolvedViewport) => void) | Result<Viewport>\n  > = []\n  for (let i = 0; i < viewportItems.length; i++) {\n    const viewportExport = viewportItems[i]\n    getResult(resolversAndResults, viewportExport)\n  }\n  return resolversAndResults\n}\n\ntype Resolved<T> = T extends Metadata ? ResolvedMetadata : ResolvedViewport\n\nfunction getResult<T extends Metadata | Viewport>(\n  resolversAndResults: Array<((value: Resolved<T>) => void) | Result<T>>,\n  exportForResult: null | T | ((parent: Promise<Resolved<T>>) => Result<T>)\n) {\n  if (typeof exportForResult === 'function') {\n    const result = exportForResult(\n      new Promise<Resolved<T>>((resolve) => resolversAndResults.push(resolve))\n    )\n    resolversAndResults.push(result)\n    if (result instanceof Promise) {\n      // since we eager execute generateMetadata and\n      // they can reject at anytime we need to ensure\n      // we attach the catch handler right away to\n      // prevent unhandled rejections crashing the process\n      result.catch((err) => {\n        return {\n          __nextError: err,\n        }\n      })\n    }\n  } else if (typeof exportForResult === 'object') {\n    resolversAndResults.push(exportForResult)\n  } else {\n    resolversAndResults.push(null)\n  }\n}\n\nfunction resolvePendingResult<\n  ResolvedType extends ResolvedMetadata | ResolvedViewport,\n>(\n  parentResult: ResolvedType,\n  resolveParentResult: (value: ResolvedType) => void\n): void {\n  // In dev we clone and freeze to prevent relying on mutating resolvedMetadata directly.\n  // In prod we just pass resolvedMetadata through without any copying.\n  if (process.env.NODE_ENV === 'development') {\n    parentResult = require('../../shared/lib/deep-freeze').deepFreeze(\n      require('./clone-metadata').cloneMetadata(parentResult)\n    )\n  }\n\n  resolveParentResult(parentResult)\n}\n\nexport async function accumulateMetadata(\n  metadataItems: MetadataItems,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const resolvedMetadata = createDefaultMetadata()\n\n  let titleTemplates: TitleTemplates = {\n    title: null,\n    twitter: null,\n    openGraph: null,\n  }\n\n  const buildState = {\n    warnings: new Set<string>(),\n  }\n\n  let favicon\n\n  // Collect the static icons in the most leaf node,\n  // since we don't collect all the static metadata icons in the parent segments.\n  const leafSegmentStaticIcons = {\n    icon: [],\n    apple: [],\n  }\n\n  const resolversAndResults = prerenderMetadata(metadataItems)\n  let resultIndex = 0\n\n  for (let i = 0; i < metadataItems.length; i++) {\n    const staticFilesMetadata = metadataItems[i][1]\n    // Treat favicon as special case, it should be the first icon in the list\n    // i <= 1 represents root layout, and if current page is also at root\n    if (i <= 1 && isFavicon(staticFilesMetadata?.icon?.[0])) {\n      const iconMod = staticFilesMetadata?.icon?.shift()\n      if (i === 0) favicon = iconMod\n    }\n\n    let pendingMetadata = resolversAndResults[resultIndex++]\n    if (typeof pendingMetadata === 'function') {\n      // This metadata item had a `generateMetadata` and\n      // we need to provide the currently resolved metadata\n      // to it before we continue;\n      const resolveParentMetadata = pendingMetadata\n      // we know that the next item is a result if this item\n      // was a resolver\n      pendingMetadata = resolversAndResults[resultIndex++] as Result<Metadata>\n\n      resolvePendingResult(resolvedMetadata, resolveParentMetadata)\n    }\n    // Otherwise the item was either null or a static export\n\n    let metadata: Metadata | null\n    if (isPromiseLike(pendingMetadata)) {\n      metadata = await pendingMetadata\n    } else {\n      metadata = pendingMetadata\n    }\n\n    mergeMetadata({\n      target: resolvedMetadata,\n      source: metadata,\n      metadataContext,\n      staticFilesMetadata,\n      titleTemplates,\n      buildState,\n      leafSegmentStaticIcons,\n    })\n\n    // If the layout is the same layer with page, skip the leaf layout and leaf page\n    // The leaf layout and page are the last two items\n    if (i < metadataItems.length - 2) {\n      titleTemplates = {\n        title: resolvedMetadata.title?.template || null,\n        openGraph: resolvedMetadata.openGraph?.title.template || null,\n        twitter: resolvedMetadata.twitter?.title.template || null,\n      }\n    }\n  }\n\n  if (\n    leafSegmentStaticIcons.icon.length > 0 ||\n    leafSegmentStaticIcons.apple.length > 0\n  ) {\n    if (!resolvedMetadata.icons) {\n      resolvedMetadata.icons = {\n        icon: [],\n        apple: [],\n      }\n      if (leafSegmentStaticIcons.icon.length > 0) {\n        resolvedMetadata.icons.icon.unshift(...leafSegmentStaticIcons.icon)\n      }\n      if (leafSegmentStaticIcons.apple.length > 0) {\n        resolvedMetadata.icons.apple.unshift(...leafSegmentStaticIcons.apple)\n      }\n    }\n  }\n\n  // Only log warnings if there are any, and only once after the metadata resolving process is finished\n  if (buildState.warnings.size > 0) {\n    for (const warning of buildState.warnings) {\n      Log.warn(warning)\n    }\n  }\n\n  return postProcessMetadata(\n    resolvedMetadata,\n    favicon,\n    titleTemplates,\n    metadataContext\n  )\n}\n\nexport async function accumulateViewport(\n  viewportItems: ViewportItems\n): Promise<ResolvedViewport> {\n  const resolvedViewport: ResolvedViewport = createDefaultViewport()\n\n  const resolversAndResults = prerenderViewport(viewportItems)\n  let i = 0\n\n  while (i < resolversAndResults.length) {\n    let pendingViewport = resolversAndResults[i++]\n    if (typeof pendingViewport === 'function') {\n      // this viewport item had a `generateViewport` and\n      // we need to provide the currently resolved viewport\n      // to it before we continue;\n      const resolveParentViewport = pendingViewport\n      // we know that the next item is a result if this item\n      // was a resolver\n      pendingViewport = resolversAndResults[i++] as Result<Viewport>\n\n      resolvePendingResult(resolvedViewport, resolveParentViewport)\n    }\n    // Otherwise the item was either null or a static export\n\n    let viewport: Viewport | null\n    if (isPromiseLike(pendingViewport)) {\n      viewport = await pendingViewport\n    } else {\n      viewport = pendingViewport\n    }\n\n    mergeViewport({\n      target: resolvedViewport,\n      source: viewport,\n    })\n  }\n  return resolvedViewport\n}\n\n// Exposed API for metadata component, that directly resolve the loader tree and related context as resolved metadata.\nexport async function resolveMetadata(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore,\n  metadataContext: MetadataContext\n): Promise<ResolvedMetadata> {\n  const metadataItems = await resolveMetadataItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n  return accumulateMetadata(metadataItems, metadataContext)\n}\n\n// Exposed API for viewport component, that directly resolve the loader tree and related context as resolved viewport.\nexport async function resolveViewport(\n  tree: LoaderTree,\n  searchParams: Promise<ParsedUrlQuery>,\n  errorConvention: MetadataErrorType | undefined,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  workStore: WorkStore\n): Promise<ResolvedViewport> {\n  const viewportItems = await resolveViewportItems(\n    tree,\n    searchParams,\n    errorConvention,\n    getDynamicParamFromSegment,\n    workStore\n  )\n  return accumulateViewport(viewportItems)\n}\n\nfunction isPromiseLike<T>(\n  value: unknown | PromiseLike<T>\n): value is PromiseLike<T> {\n  return (\n    typeof value === 'object' &&\n    value !== null &&\n    typeof (value as PromiseLike<unknown>).then === 'function'\n  )\n}\n"], "names": ["accumulateMetadata", "accumulateViewport", "resolveMetadata", "resolveViewport", "isFavicon", "icon", "url", "toString", "startsWith", "type", "mergeStaticMetadata", "source", "target", "staticFilesMetadata", "metadataContext", "titleTemplates", "leafSegmentStaticIcons", "apple", "openGraph", "twitter", "manifest", "hasOwnProperty", "resolvedTwitter", "resolveTwitter", "images", "metadataBase", "isStaticMetadataRouteFile", "resolvedOpenGraph", "resolveOpenGraph", "mergeMetadata", "buildState", "key_", "key", "title", "resolveTitle", "alternates", "resolveAlternates", "facebook", "resolveFacebook", "verification", "resolveVerification", "icons", "resolveIcons", "appleWebApp", "resolveAppleWebApp", "appLinks", "resolveAppLinks", "robots", "resolveRobots", "resolveAsArrayOrUndefined", "authors", "resolveItunes", "itunes", "pagination", "resolvePagination", "other", "Object", "assign", "warnings", "add", "pathname", "mergeViewport", "themeColor", "resolveThemeColor", "colorScheme", "getDefinedViewport", "mod", "props", "tracingProps", "generateViewport", "route", "parent", "getTracer", "trace", "ResolveMetadataSpan", "spanName", "attributes", "viewport", "getDefinedMetadata", "generateMetadata", "metadata", "collectStaticImagesFiles", "undefined", "iconPromises", "map", "imageModule", "interopDefault", "length", "Promise", "all", "flat", "resolveStaticMetadata", "modules", "staticMetadata", "collectMetadata", "tree", "metadataItems", "errorMetadataItem", "errorConvention", "modType", "hasErrorConventionComponent", "Boolean", "getComponentTypeModule", "layoutOrPageMod", "layoutOrPageModType", "getLayoutOrPageModule", "metadataExport", "push", "errorMod", "errorMetadataExport", "collectViewport", "viewportItems", "errorViewportItemRef", "viewportExport", "errorViewportExport", "current", "resolveMetadataItems", "cache", "searchParams", "getDynamicParamFromSegment", "workStore", "parentParams", "treePrefix", "resolveMetadataItemsImpl", "segment", "parallelRoutes", "page", "currentTreePrefix", "isPage", "segmentParam", "currentParams", "value", "param", "params", "createServerParamsForMetadata", "layerProps", "filter", "s", "PAGE_SEGMENT_KEY", "join", "childTree", "keys", "resolveViewportItems", "resolveViewportItemsImpl", "isTitleTruthy", "absolute", "hasTitle", "inheritFromMetadata", "description", "commonOgKeys", "postProcessMetadata", "favicon", "autoFillProps", "hasTwTitle", "hasTwDescription", "hasTwImages", "partialTwitter", "unshift", "prerenderMetadata", "resolversAndResults", "i", "getResult", "prerenderViewport", "exportForResult", "result", "resolve", "catch", "err", "__nextError", "resolvePendingResult", "parentResult", "resolveParentResult", "process", "env", "NODE_ENV", "require", "deepFreeze", "cloneMetadata", "resolvedMetadata", "createDefaultMetadata", "Set", "resultIndex", "iconMod", "shift", "pendingMetadata", "resolveParentMetadata", "isPromiseLike", "template", "size", "warning", "Log", "warn", "resolvedViewport", "createDefaultViewport", "pendingViewport", "resolveParentViewport", "then"], "mappings": ";;;;;;;;;;;;;;;;;IAw4BsBA,kBAAkB;eAAlBA;;IAgHAC,kBAAkB;eAAlBA;;IAuCAC,eAAe;eAAfA;;IAmBAC,eAAe;eAAfA;;;QAxhCf;uBAEe;iCAIf;kCAC0C;8BACpB;uBACa;8BAInC;gCACwB;+BAWxB;8BACsB;wBACH;2BACU;yBACH;6DACZ;wBACyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqC9C,SAASC,UAAUC,IAAgC;IACjD,IAAI,CAACA,MAAM;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,OACE,AAACA,CAAAA,KAAKC,GAAG,KAAK,kBACZD,KAAKC,GAAG,CAACC,QAAQ,GAAGC,UAAU,CAAC,gBAAe,KAChDH,KAAKI,IAAI,KAAK;AAElB;AAEA,SAASC,oBACPC,MAAuB,EACvBC,MAAwB,EACxBC,mBAAmC,EACnCC,eAAgC,EAChCC,cAA8B,EAC9BC,sBAAmC;QAenBL,iBAWEA;IAxBlB,IAAI,CAACE,qBAAqB;IAC1B,MAAM,EAAER,IAAI,EAAEY,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAE,GAAGP;IAEtD,uDAAuD;IAEvD,IAAIR,MAAM;QACRW,uBAAuBX,IAAI,GAAGA;IAChC;IACA,IAAIY,OAAO;QACTD,uBAAuBC,KAAK,GAAGA;IACjC;IAEA,8FAA8F;IAC9F,IAAIE,WAAW,EAACR,2BAAAA,kBAAAA,OAAQQ,OAAO,qBAAfR,gBAAiBU,cAAc,CAAC,YAAW;QACzD,MAAMC,kBAAkBC,IAAAA,gCAAc,EACpC;YAAE,GAAGX,OAAOO,OAAO;YAAEK,QAAQL;QAAQ,GACrCP,OAAOa,YAAY,EACnB;YAAE,GAAGX,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeI,OAAO;QAExBP,OAAOO,OAAO,GAAGG;IACnB;IAEA,gGAAgG;IAChG,IAAIJ,aAAa,EAACP,2BAAAA,oBAAAA,OAAQO,SAAS,qBAAjBP,kBAAmBU,cAAc,CAAC,YAAW;QAC7D,MAAMM,oBAAoBC,IAAAA,kCAAgB,EACxC;YAAE,GAAGhB,OAAOM,SAAS;YAAEM,QAAQN;QAAU,GACzCN,OAAOa,YAAY,EACnB;YAAE,GAAGX,eAAe;YAAEY,2BAA2B;QAAK,GACtDX,eAAeG,SAAS;QAE1BN,OAAOM,SAAS,GAAGS;IACrB;IACA,IAAIP,UAAU;QACZR,OAAOQ,QAAQ,GAAGA;IACpB;IAEA,OAAOR;AACT;AAEA,+DAA+D;AAC/D,SAASiB,cAAc,EACrBlB,MAAM,EACNC,MAAM,EACNC,mBAAmB,EACnBE,cAAc,EACdD,eAAe,EACfgB,UAAU,EACVd,sBAAsB,EASvB;IACC,sFAAsF;IACtF,MAAMS,eACJ,QAAOd,0BAAAA,OAAQc,YAAY,MAAK,cAC5Bd,OAAOc,YAAY,GACnBb,OAAOa,YAAY;IACzB,IAAK,MAAMM,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAS;oBACZpB,OAAOqB,KAAK,GAAGC,IAAAA,0BAAY,EAACvB,OAAOsB,KAAK,EAAElB,eAAekB,KAAK;oBAC9D;gBACF;YACA,KAAK;gBAAc;oBACjBrB,OAAOuB,UAAU,GAAGC,IAAAA,gCAAiB,EACnCzB,OAAOwB,UAAU,EACjBV,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAa;oBAChBF,OAAOM,SAAS,GAAGU,IAAAA,kCAAgB,EACjCjB,OAAOO,SAAS,EAChBO,cACAX,iBACAC,eAAeG,SAAS;oBAE1B;gBACF;YACA,KAAK;gBAAW;oBACdN,OAAOO,OAAO,GAAGI,IAAAA,gCAAc,EAC7BZ,OAAOQ,OAAO,EACdM,cACAX,iBACAC,eAAeI,OAAO;oBAExB;gBACF;YACA,KAAK;gBACHP,OAAOyB,QAAQ,GAAGC,IAAAA,8BAAe,EAAC3B,OAAO0B,QAAQ;gBACjD;YACF,KAAK;gBACHzB,OAAO2B,YAAY,GAAGC,IAAAA,kCAAmB,EAAC7B,OAAO4B,YAAY;gBAC7D;YAEF,KAAK;gBAAS;oBACZ3B,OAAO6B,KAAK,GAAGC,IAAAA,0BAAY,EAAC/B,OAAO8B,KAAK;oBACxC;gBACF;YACA,KAAK;gBACH7B,OAAO+B,WAAW,GAAGC,IAAAA,iCAAkB,EAACjC,OAAOgC,WAAW;gBAC1D;YACF,KAAK;gBACH/B,OAAOiC,QAAQ,GAAGC,IAAAA,8BAAe,EAACnC,OAAOkC,QAAQ;gBACjD;YACF,KAAK;gBAAU;oBACbjC,OAAOmC,MAAM,GAAGC,IAAAA,4BAAa,EAACrC,OAAOoC,MAAM;oBAC3C;gBACF;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAY;oBACfnC,MAAM,CAACoB,IAAI,GAAGiB,IAAAA,gCAAyB,EAACtC,MAAM,CAACqB,IAAI;oBACnD;gBACF;YACA,KAAK;gBAAW;oBACdpB,MAAM,CAACoB,IAAI,GAAGiB,IAAAA,gCAAyB,EAACtC,OAAOuC,OAAO;oBACtD;gBACF;YACA,KAAK;gBAAU;oBACbtC,MAAM,CAACoB,IAAI,GAAGmB,IAAAA,4BAAa,EACzBxC,OAAOyC,MAAM,EACb3B,cACAX;oBAEF;gBACF;YACA,KAAK;gBAAc;oBACjBF,OAAOyC,UAAU,GAAGC,IAAAA,gCAAiB,EACnC3C,OAAO0C,UAAU,EACjB5B,cACAX;oBAEF;gBACF;YACA,+CAA+C;YAC/C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qCAAqC;gBACrCF,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI,IAAI;gBAC7B;YACF,KAAK;gBACHpB,OAAO2C,KAAK,GAAGC,OAAOC,MAAM,CAAC,CAAC,GAAG7C,OAAO2C,KAAK,EAAE5C,OAAO4C,KAAK;gBAC3D;YACF,KAAK;gBACH3C,OAAOa,YAAY,GAAGA;gBACtB;YAEF;gBAAS;oBACP,IACE,AAACO,CAAAA,QAAQ,cACPA,QAAQ,gBACRA,QAAQ,aAAY,KACtBrB,MAAM,CAACqB,IAAI,IAAI,MACf;wBACAF,WAAW4B,QAAQ,CAACC,GAAG,CACrB,CAAC,qBAAqB,EAAE3B,IAAI,qCAAqC,EAAElB,gBAAgB8C,QAAQ,CAAC,8HAA8H,CAAC;oBAE/N;oBACA;gBACF;QACF;IACF;IACAlD,oBACEC,QACAC,QACAC,qBACAC,iBACAC,gBACAC;AAEJ;AAEA,SAAS6C,cAAc,EACrBjD,MAAM,EACND,MAAM,EAIP;IACC,IAAI,CAACA,QAAQ;IACb,IAAK,MAAMoB,QAAQpB,OAAQ;QACzB,MAAMqB,MAAMD;QAEZ,OAAQC;YACN,KAAK;gBAAc;oBACjBpB,OAAOkD,UAAU,GAAGC,IAAAA,gCAAiB,EAACpD,OAAOmD,UAAU;oBACvD;gBACF;YACA,KAAK;gBACHlD,OAAOoD,WAAW,GAAGrD,OAAOqD,WAAW,IAAI;gBAC3C;YACF;gBACE,6CAA6C;gBAC7C,iCAAiC;gBACjCpD,MAAM,CAACoB,IAAI,GAAGrB,MAAM,CAACqB,IAAI;gBACzB;QACJ;IACF;AACF;AAEA,SAASiC,mBACPC,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIG,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAEC,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACL,gBAAgB,EACpC;gBACEM,UAAU,CAAC,iBAAiB,EAAEL,OAAO;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAMJ,IAAIG,gBAAgB,CAACF,OAAOI;IAExC;IACA,OAAOL,IAAIW,QAAQ,IAAI;AACzB;AAEA,SAASC,mBACPZ,GAAQ,EACRC,KAAU,EACVC,YAA+B;IAE/B,IAAI,OAAOF,IAAIa,gBAAgB,KAAK,YAAY;QAC9C,MAAM,EAAET,KAAK,EAAE,GAAGF;QAClB,OAAO,CAACG,SACNC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,8BAAmB,CAACK,gBAAgB,EACpC;gBACEJ,UAAU,CAAC,iBAAiB,EAAEL,OAAO;gBACrCM,YAAY;oBACV,aAAaN;gBACf;YACF,GACA,IAAMJ,IAAIa,gBAAgB,CAACZ,OAAOI;IAExC;IACA,OAAOL,IAAIc,QAAQ,IAAI;AACzB;AAEA,eAAeC,yBACbD,QAAmC,EACnCb,KAAU,EACV1D,IAAkD;QAU7C;IARL,IAAI,EAACuE,4BAAAA,QAAU,CAACvE,KAAK,GAAE,OAAOyE;IAE9B,MAAMC,eAAeH,QAAQ,CAACvE,KAAyB,CAAC2E,GAAG,CACzD,OAAOC,cACLC,IAAAA,8BAAc,EAAC,MAAMD,YAAYlB;IAGrC,OAAOgB,CAAAA,gCAAAA,aAAcI,MAAM,IAAG,KACzB,QAAA,MAAMC,QAAQC,GAAG,CAACN,kCAAnB,AAAC,MAAkCO,IAAI,KACvCR;AACN;AAEA,eAAeS,sBACbC,OAAsB,EACtBzB,KAAU;IAEV,MAAM,EAAEa,QAAQ,EAAE,GAAGY;IACrB,IAAI,CAACZ,UAAU,OAAO;IAEtB,MAAM,CAAC3E,MAAMY,OAAOC,WAAWC,QAAQ,GAAG,MAAMqE,QAAQC,GAAG,CAAC;QAC1DR,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;QAC1Cc,yBAAyBD,UAAUb,OAAO;KAC3C;IAED,MAAM0B,iBAAiB;QACrBxF;QACAY;QACAC;QACAC;QACAC,UAAU4D,SAAS5D,QAAQ;IAC7B;IAEA,OAAOyE;AACT;AAEA,4FAA4F;AAC5F,eAAeC,gBAAgB,EAC7BC,IAAI,EACJC,aAAa,EACbC,iBAAiB,EACjB9B,KAAK,EACLG,KAAK,EACL4B,eAAe,EAQhB;IACC,IAAIhC;IACJ,IAAIiC;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnBhC,MAAM,MAAMoC,IAAAA,oCAAsB,EAACP,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAEhC,KAAKqC,eAAe,EAAEJ,SAASK,mBAAmB,EAAE,GAC1D,MAAMC,IAAAA,mCAAqB,EAACV;QAC9B7B,MAAMqC;QACNJ,UAAUK;IACZ;IAEA,IAAIL,SAAS;QACX7B,SAAS,CAAC,CAAC,EAAE6B,SAAS;IACxB;IAEA,MAAMtF,sBAAsB,MAAM8E,sBAAsBI,IAAI,CAAC,EAAE,EAAE5B;IACjE,MAAMuC,iBAAiBxC,MAAMY,mBAAmBZ,KAAKC,OAAO;QAAEG;IAAM,KAAK;IAEzE0B,cAAcW,IAAI,CAAC;QAACD;QAAgB7F;KAAoB;IAExD,IAAIuF,+BAA+BF,iBAAiB;QAClD,MAAMU,WAAW,MAAMN,IAAAA,oCAAsB,EAACP,MAAMG;QACpD,MAAMW,sBAAsBD,WACxB9B,mBAAmB8B,UAAUzC,OAAO;YAAEG;QAAM,KAC5C;QAEJ2B,iBAAiB,CAAC,EAAE,GAAGY;QACvBZ,iBAAiB,CAAC,EAAE,GAAGpF;IACzB;AACF;AAEA,4FAA4F;AAC5F,eAAeiG,gBAAgB,EAC7Bf,IAAI,EACJgB,aAAa,EACbC,oBAAoB,EACpB7C,KAAK,EACLG,KAAK,EACL4B,eAAe,EAQhB;IACC,IAAIhC;IACJ,IAAIiC;IACJ,MAAMC,8BAA8BC,QAClCH,mBAAmBH,IAAI,CAAC,EAAE,CAACG,gBAAgB;IAE7C,IAAIA,iBAAiB;QACnBhC,MAAM,MAAMoC,IAAAA,oCAAsB,EAACP,MAAM;QACzCI,UAAUD;IACZ,OAAO;QACL,MAAM,EAAEhC,KAAKqC,eAAe,EAAEJ,SAASK,mBAAmB,EAAE,GAC1D,MAAMC,IAAAA,mCAAqB,EAACV;QAC9B7B,MAAMqC;QACNJ,UAAUK;IACZ;IAEA,IAAIL,SAAS;QACX7B,SAAS,CAAC,CAAC,EAAE6B,SAAS;IACxB;IAEA,MAAMc,iBAAiB/C,MAAMD,mBAAmBC,KAAKC,OAAO;QAAEG;IAAM,KAAK;IAEzEyC,cAAcJ,IAAI,CAACM;IAEnB,IAAIb,+BAA+BF,iBAAiB;QAClD,MAAMU,WAAW,MAAMN,IAAAA,oCAAsB,EAACP,MAAMG;QACpD,MAAMgB,sBAAsBN,WACxB3C,mBAAmB2C,UAAUzC,OAAO;YAAEG;QAAM,KAC5C;QAEJ0C,qBAAqBG,OAAO,GAAGD;IACjC;AACF;AAEA,MAAME,uBAAuBC,IAAAA,YAAK,EAAC,eACjCtB,IAAgB,EAChBuB,YAAqC,EACrCpB,eAA8C,EAC9CqB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMzB,gBAA+B,EAAE;IACvC,MAAMC,oBAA2C;QAAC;QAAM;KAAK;IAC7D,MAAMyB,aAAaxC;IACnB,OAAOyC,yBACL3B,eACAD,MACA2B,YACAD,cACAH,cACApB,iBACAD,mBACAsB,4BACAC;AAEJ;AAEA,eAAeG,yBACb3B,aAA4B,EAC5BD,IAAgB,EAChB,6FAA6F,GAC7F2B,UAAgC,EAChCD,YAAoB,EACpBH,YAAqC,EACrCpB,eAA8C,EAC9CD,iBAAwC,EACxCsB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG/B;IAC5C,MAAMgC,oBACJL,cAAcA,WAAWnC,MAAM,GAAG;WAAImC;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeV,2BAA2BK;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAASC,IAAAA,qCAA6B,EAACJ,eAAeV;IAE5D,IAAIe;IACJ,IAAIP,QAAQ;QACVO,aAAa;YACXF;YACAf;QACF;IACF,OAAO;QACLiB,aAAa;YACXF;QACF;IACF;IAEA,MAAMvC,gBAAgB;QACpBC;QACAC;QACAC;QACAC;QACA/B,OAAOoE;QACPjE,OAAOyD,iBACL,yCAAyC;SACxCS,MAAM,CAAC,CAACC,IAAMA,MAAMC,yBAAgB,EACpCC,IAAI,CAAC;IACV;IAEA,IAAK,MAAM3G,OAAO6F,eAAgB;QAChC,MAAMe,YAAYf,cAAc,CAAC7F,IAAI;QACrC,MAAM2F,yBACJ3B,eACA4C,WACAb,mBACAG,eACAZ,cACApB,iBACAD,mBACAsB,4BACAC;IAEJ;IAEA,IAAIhE,OAAOqF,IAAI,CAAChB,gBAAgBtC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCF,cAAcW,IAAI,CAACV;IACrB;IAEA,OAAOD;AACT;AAGA,MAAM8C,uBAAuBzB,IAAAA,YAAK,EAAC,eACjCtB,IAAgB,EAChBuB,YAAqC,EACrCpB,eAA8C,EAC9CqB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMC,eAAe,CAAC;IACtB,MAAMV,gBAA+B,EAAE;IACvC,MAAMC,uBAA6C;QACjDG,SAAS;IACX;IACA,MAAMO,aAAaxC;IACnB,OAAO6D,yBACLhC,eACAhB,MACA2B,YACAD,cACAH,cACApB,iBACAc,sBACAO,4BACAC;AAEJ;AAEA,eAAeuB,yBACbhC,aAA4B,EAC5BhB,IAAgB,EAChB,6FAA6F,GAC7F2B,UAAgC,EAChCD,YAAoB,EACpBH,YAAqC,EACrCpB,eAA8C,EAC9Cc,oBAA0C,EAC1CO,0BAAsD,EACtDC,SAAoB;IAEpB,MAAM,CAACI,SAASC,gBAAgB,EAAEC,IAAI,EAAE,CAAC,GAAG/B;IAC5C,MAAMgC,oBACJL,cAAcA,WAAWnC,MAAM,GAAG;WAAImC;QAAYE;KAAQ,GAAG;QAACA;KAAQ;IACxE,MAAMI,SAAS,OAAOF,SAAS;IAE/B,iCAAiC;IACjC,MAAMG,eAAeV,2BAA2BK;IAChD;;GAEC,GACD,IAAIM,gBAAgBT;IACpB,IAAIQ,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGT,YAAY;YACf,CAACQ,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAME,SAASC,IAAAA,qCAA6B,EAACJ,eAAeV;IAE5D,IAAIe;IACJ,IAAIP,QAAQ;QACVO,aAAa;YACXF;YACAf;QACF;IACF,OAAO;QACLiB,aAAa;YACXF;QACF;IACF;IAEA,MAAMvB,gBAAgB;QACpBf;QACAgB;QACAC;QACAd;QACA/B,OAAOoE;QACPjE,OAAOyD,iBACL,yCAAyC;SACxCS,MAAM,CAAC,CAACC,IAAMA,MAAMC,yBAAgB,EACpCC,IAAI,CAAC;IACV;IAEA,IAAK,MAAM3G,OAAO6F,eAAgB;QAChC,MAAMe,YAAYf,cAAc,CAAC7F,IAAI;QACrC,MAAM+G,yBACJhC,eACA6B,WACAb,mBACAG,eACAZ,cACApB,iBACAc,sBACAO,4BACAC;IAEJ;IAEA,IAAIhE,OAAOqF,IAAI,CAAChB,gBAAgBtC,MAAM,KAAK,KAAKW,iBAAiB;QAC/D,0EAA0E;QAC1E,qCAAqC;QACrCa,cAAcJ,IAAI,CAACK,qBAAqBG,OAAO;IACjD;IAEA,OAAOJ;AACT;AAKA,MAAMiC,gBAAgB,CAAC/G,QACrB,CAAC,EAACA,yBAAAA,MAAOgH,QAAQ;AACnB,MAAMC,WAAW,CAAClE,WAA+BgE,cAAchE,4BAAAA,SAAU/C,KAAK;AAE9E,SAASkH,oBACPvI,MAA4C,EAC5CoE,QAA0B;IAE1B,IAAIpE,QAAQ;QACV,IAAI,CAACsI,SAAStI,WAAWsI,SAASlE,WAAW;YAC3CpE,OAAOqB,KAAK,GAAG+C,SAAS/C,KAAK;QAC/B;QACA,IAAI,CAACrB,OAAOwI,WAAW,IAAIpE,SAASoE,WAAW,EAAE;YAC/CxI,OAAOwI,WAAW,GAAGpE,SAASoE,WAAW;QAC3C;IACF;AACF;AAEA,6DAA6D;AAC7D,MAAMC,eAAe;IAAC;IAAS;IAAe;CAAS;AACvD,SAASC,oBACPtE,QAA0B,EAC1BuE,OAAY,EACZxI,cAA8B,EAC9BD,eAAgC;IAEhC,MAAM,EAAEI,SAAS,EAAEC,OAAO,EAAE,GAAG6D;IAE/B,IAAI9D,WAAW;QACb,kEAAkE;QAClE,wCAAwC;QACxC,IAAIsI,gBAIC,CAAC;QACN,MAAMC,aAAaP,SAAS/H;QAC5B,MAAMuI,mBAAmBvI,2BAAAA,QAASiI,WAAW;QAC7C,MAAMO,cAActD,QAClBlF,CAAAA,2BAAAA,QAASE,cAAc,CAAC,cAAaF,QAAQK,MAAM;QAErD,IAAI,CAACiI,YAAY;YACf,IAAIT,cAAc9H,UAAUe,KAAK,GAAG;gBAClCuH,cAAcvH,KAAK,GAAGf,UAAUe,KAAK;YACvC,OAAO,IAAI+C,SAAS/C,KAAK,IAAI+G,cAAchE,SAAS/C,KAAK,GAAG;gBAC1DuH,cAAcvH,KAAK,GAAG+C,SAAS/C,KAAK;YACtC;QACF;QACA,IAAI,CAACyH,kBACHF,cAAcJ,WAAW,GACvBlI,UAAUkI,WAAW,IAAIpE,SAASoE,WAAW,IAAIlE;QACrD,IAAI,CAACyE,aAAaH,cAAchI,MAAM,GAAGN,UAAUM,MAAM;QAEzD,IAAIgC,OAAOqF,IAAI,CAACW,eAAejE,MAAM,GAAG,GAAG;YACzC,MAAMqE,iBAAiBrI,IAAAA,gCAAc,EACnCiI,eACAxE,SAASvD,YAAY,EACrBX,iBACAC,eAAeI,OAAO;YAExB,IAAI6D,SAAS7D,OAAO,EAAE;gBACpB6D,SAAS7D,OAAO,GAAGqC,OAAOC,MAAM,CAAC,CAAC,GAAGuB,SAAS7D,OAAO,EAAE;oBACrD,GAAI,CAACsI,cAAc;wBAAExH,KAAK,EAAE2H,kCAAAA,eAAgB3H,KAAK;oBAAC,CAAC;oBACnD,GAAI,CAACyH,oBAAoB;wBACvBN,WAAW,EAAEQ,kCAAAA,eAAgBR,WAAW;oBAC1C,CAAC;oBACD,GAAI,CAACO,eAAe;wBAAEnI,MAAM,EAAEoI,kCAAAA,eAAgBpI,MAAM;oBAAC,CAAC;gBACxD;YACF,OAAO;gBACLwD,SAAS7D,OAAO,GAAGyI;YACrB;QACF;IACF;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/CT,oBAAoBjI,WAAW8D;IAC/BmE,oBAAoBhI,SAAS6D;IAE7B,IAAIuE,SAAS;QACX,IAAI,CAACvE,SAASvC,KAAK,EAAE;YACnBuC,SAASvC,KAAK,GAAG;gBACfpC,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;QACF;QAEA+D,SAASvC,KAAK,CAACpC,IAAI,CAACwJ,OAAO,CAACN;IAC9B;IAEA,OAAOvE;AACT;AAIA,SAAS8E,kBAAkB9D,aAA4B;IACrD,qEAAqE;IACrE,+EAA+E;IAC/E,UAAU;IACV,MAAM+D,sBAEF,EAAE;IACN,IAAK,IAAIC,IAAI,GAAGA,IAAIhE,cAAcT,MAAM,EAAEyE,IAAK;QAC7C,MAAMtD,iBAAiBV,aAAa,CAACgE,EAAE,CAAC,EAAE;QAC1CC,UAAUF,qBAAqBrD;IACjC;IACA,OAAOqD;AACT;AAEA,SAASG,kBAAkBnD,aAA4B;IACrD,qEAAqE;IACrE,+EAA+E;IAC/E,UAAU;IACV,MAAMgD,sBAEF,EAAE;IACN,IAAK,IAAIC,IAAI,GAAGA,IAAIjD,cAAcxB,MAAM,EAAEyE,IAAK;QAC7C,MAAM/C,iBAAiBF,aAAa,CAACiD,EAAE;QACvCC,UAAUF,qBAAqB9C;IACjC;IACA,OAAO8C;AACT;AAIA,SAASE,UACPF,mBAAsE,EACtEI,eAAyE;IAEzE,IAAI,OAAOA,oBAAoB,YAAY;QACzC,MAAMC,SAASD,gBACb,IAAI3E,QAAqB,CAAC6E,UAAYN,oBAAoBpD,IAAI,CAAC0D;QAEjEN,oBAAoBpD,IAAI,CAACyD;QACzB,IAAIA,kBAAkB5E,SAAS;YAC7B,8CAA8C;YAC9C,+CAA+C;YAC/C,4CAA4C;YAC5C,oDAAoD;YACpD4E,OAAOE,KAAK,CAAC,CAACC;gBACZ,OAAO;oBACLC,aAAaD;gBACf;YACF;QACF;IACF,OAAO,IAAI,OAAOJ,oBAAoB,UAAU;QAC9CJ,oBAAoBpD,IAAI,CAACwD;IAC3B,OAAO;QACLJ,oBAAoBpD,IAAI,CAAC;IAC3B;AACF;AAEA,SAAS8D,qBAGPC,YAA0B,EAC1BC,mBAAkD;IAElD,uFAAuF;IACvF,qEAAqE;IACrE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CJ,eAAeK,QAAQ,gCAAgCC,UAAU,CAC/DD,QAAQ,oBAAoBE,aAAa,CAACP;IAE9C;IAEAC,oBAAoBD;AACtB;AAEO,eAAe1K,mBACpBgG,aAA4B,EAC5BlF,eAAgC;IAEhC,MAAMoK,mBAAmBC,IAAAA,sCAAqB;IAE9C,IAAIpK,iBAAiC;QACnCkB,OAAO;QACPd,SAAS;QACTD,WAAW;IACb;IAEA,MAAMY,aAAa;QACjB4B,UAAU,IAAI0H;IAChB;IAEA,IAAI7B;IAEJ,kDAAkD;IAClD,+EAA+E;IAC/E,MAAMvI,yBAAyB;QAC7BX,MAAM,EAAE;QACRY,OAAO,EAAE;IACX;IAEA,MAAM8I,sBAAsBD,kBAAkB9D;IAC9C,IAAIqF,cAAc;IAElB,IAAK,IAAIrB,IAAI,GAAGA,IAAIhE,cAAcT,MAAM,EAAEyE,IAAK;YAIrBnJ;QAHxB,MAAMA,sBAAsBmF,aAAa,CAACgE,EAAE,CAAC,EAAE;QAC/C,yEAAyE;QACzE,qEAAqE;QACrE,IAAIA,KAAK,KAAK5J,UAAUS,wCAAAA,4BAAAA,oBAAqBR,IAAI,qBAAzBQ,yBAA2B,CAAC,EAAE,GAAG;gBACvCA;YAAhB,MAAMyK,UAAUzK,wCAAAA,6BAAAA,oBAAqBR,IAAI,qBAAzBQ,2BAA2B0K,KAAK;YAChD,IAAIvB,MAAM,GAAGT,UAAU+B;QACzB;QAEA,IAAIE,kBAAkBzB,mBAAmB,CAACsB,cAAc;QACxD,IAAI,OAAOG,oBAAoB,YAAY;YACzC,kDAAkD;YAClD,qDAAqD;YACrD,4BAA4B;YAC5B,MAAMC,wBAAwBD;YAC9B,sDAAsD;YACtD,iBAAiB;YACjBA,kBAAkBzB,mBAAmB,CAACsB,cAAc;YAEpDZ,qBAAqBS,kBAAkBO;QACzC;QACA,wDAAwD;QAExD,IAAIzG;QACJ,IAAI0G,cAAcF,kBAAkB;YAClCxG,WAAW,MAAMwG;QACnB,OAAO;YACLxG,WAAWwG;QACb;QAEA3J,cAAc;YACZjB,QAAQsK;YACRvK,QAAQqE;YACRlE;YACAD;YACAE;YACAe;YACAd;QACF;QAEA,gFAAgF;QAChF,kDAAkD;QAClD,IAAIgJ,IAAIhE,cAAcT,MAAM,GAAG,GAAG;gBAEvB2F,yBACIA,6BACFA;YAHXnK,iBAAiB;gBACfkB,OAAOiJ,EAAAA,0BAAAA,iBAAiBjJ,KAAK,qBAAtBiJ,wBAAwBS,QAAQ,KAAI;gBAC3CzK,WAAWgK,EAAAA,8BAAAA,iBAAiBhK,SAAS,qBAA1BgK,4BAA4BjJ,KAAK,CAAC0J,QAAQ,KAAI;gBACzDxK,SAAS+J,EAAAA,4BAAAA,iBAAiB/J,OAAO,qBAAxB+J,0BAA0BjJ,KAAK,CAAC0J,QAAQ,KAAI;YACvD;QACF;IACF;IAEA,IACE3K,uBAAuBX,IAAI,CAACkF,MAAM,GAAG,KACrCvE,uBAAuBC,KAAK,CAACsE,MAAM,GAAG,GACtC;QACA,IAAI,CAAC2F,iBAAiBzI,KAAK,EAAE;YAC3ByI,iBAAiBzI,KAAK,GAAG;gBACvBpC,MAAM,EAAE;gBACRY,OAAO,EAAE;YACX;YACA,IAAID,uBAAuBX,IAAI,CAACkF,MAAM,GAAG,GAAG;gBAC1C2F,iBAAiBzI,KAAK,CAACpC,IAAI,CAACwJ,OAAO,IAAI7I,uBAAuBX,IAAI;YACpE;YACA,IAAIW,uBAAuBC,KAAK,CAACsE,MAAM,GAAG,GAAG;gBAC3C2F,iBAAiBzI,KAAK,CAACxB,KAAK,CAAC4I,OAAO,IAAI7I,uBAAuBC,KAAK;YACtE;QACF;IACF;IAEA,qGAAqG;IACrG,IAAIa,WAAW4B,QAAQ,CAACkI,IAAI,GAAG,GAAG;QAChC,KAAK,MAAMC,WAAW/J,WAAW4B,QAAQ,CAAE;YACzCoI,KAAIC,IAAI,CAACF;QACX;IACF;IAEA,OAAOvC,oBACL4B,kBACA3B,SACAxI,gBACAD;AAEJ;AAEO,eAAeb,mBACpB8G,aAA4B;IAE5B,MAAMiF,mBAAqCC,IAAAA,sCAAqB;IAEhE,MAAMlC,sBAAsBG,kBAAkBnD;IAC9C,IAAIiD,IAAI;IAER,MAAOA,IAAID,oBAAoBxE,MAAM,CAAE;QACrC,IAAI2G,kBAAkBnC,mBAAmB,CAACC,IAAI;QAC9C,IAAI,OAAOkC,oBAAoB,YAAY;YACzC,kDAAkD;YAClD,qDAAqD;YACrD,4BAA4B;YAC5B,MAAMC,wBAAwBD;YAC9B,sDAAsD;YACtD,iBAAiB;YACjBA,kBAAkBnC,mBAAmB,CAACC,IAAI;YAE1CS,qBAAqBuB,kBAAkBG;QACzC;QACA,wDAAwD;QAExD,IAAItH;QACJ,IAAI6G,cAAcQ,kBAAkB;YAClCrH,WAAW,MAAMqH;QACnB,OAAO;YACLrH,WAAWqH;QACb;QAEArI,cAAc;YACZjD,QAAQoL;YACRrL,QAAQkE;QACV;IACF;IACA,OAAOmH;AACT;AAGO,eAAe9L,gBACpB6F,IAAgB,EAChBuB,YAAqC,EACrCpB,eAA8C,EAC9CqB,0BAAsD,EACtDC,SAAoB,EACpB1G,eAAgC;IAEhC,MAAMkF,gBAAgB,MAAMoB,qBAC1BrB,MACAuB,cACApB,iBACAqB,4BACAC;IAEF,OAAOxH,mBAAmBgG,eAAelF;AAC3C;AAGO,eAAeX,gBACpB4F,IAAgB,EAChBuB,YAAqC,EACrCpB,eAA8C,EAC9CqB,0BAAsD,EACtDC,SAAoB;IAEpB,MAAMT,gBAAgB,MAAM+B,qBAC1B/C,MACAuB,cACApB,iBACAqB,4BACAC;IAEF,OAAOvH,mBAAmB8G;AAC5B;AAEA,SAAS2E,cACPvD,KAA+B;IAE/B,OACE,OAAOA,UAAU,YACjBA,UAAU,QACV,OAAO,AAACA,MAA+BiE,IAAI,KAAK;AAEpD"}