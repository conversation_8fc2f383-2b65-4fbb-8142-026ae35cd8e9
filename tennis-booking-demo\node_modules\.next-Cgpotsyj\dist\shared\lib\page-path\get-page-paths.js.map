{"version": 3, "sources": ["../../../../src/shared/lib/page-path/get-page-paths.ts"], "sourcesContent": ["import { denormalizePagePath } from './denormalize-page-path'\nimport path from '../isomorphic/path'\n\n/**\n * Calculate all possible pagePaths for a given normalized pagePath along with\n * allowed extensions. This can be used to check which one of the files exists\n * and to debug inspected locations.\n *\n * For pages, map `/route` to [`/route.[ext]`, `/route/index.[ext]`]\n * For app paths, map `/route/page` to [`/route/page.[ext]`] or `/route/route`\n * to [`/route/route.[ext]`]\n *\n * @param normalizedPagePath Normalized page path (it will denormalize).\n * @param extensions Allowed extensions.\n */\nexport function getPagePaths(\n  normalizedPagePath: string,\n  extensions: string[],\n  isAppDir: boolean\n) {\n  const page = denormalizePagePath(normalizedPagePath)\n\n  let prefixes: string[]\n  if (isAppDir) {\n    prefixes = [page]\n  } else if (normalizedPagePath.endsWith('/index')) {\n    prefixes = [path.join(page, 'index')]\n  } else {\n    prefixes = [page, path.join(page, 'index')]\n  }\n\n  const paths: string[] = []\n  for (const extension of extensions) {\n    for (const prefix of prefixes) {\n      paths.push(`${prefix}.${extension}`)\n    }\n  }\n\n  return paths\n}\n"], "names": ["getPagePaths", "normalizedPagePath", "extensions", "isAppDir", "page", "denormalizePagePath", "prefixes", "endsWith", "path", "join", "paths", "extension", "prefix", "push"], "mappings": ";;;;+BAeg<PERSON>;;;eAAAA;;;;qCAfoB;+DACnB;AAcV,SAASA,aACdC,kBAA0B,EAC1BC,UAAoB,EACpBC,QAAiB;IAEjB,MAAMC,OAAOC,IAAAA,wCAAmB,EAACJ;IAEjC,IAAIK;IACJ,IAAIH,UAAU;QACZG,WAAW;YAACF;SAAK;IACnB,OAAO,IAAIH,mBAAmBM,QAAQ,CAAC,WAAW;QAChDD,WAAW;YAACE,aAAI,CAACC,IAAI,CAACL,MAAM;SAAS;IACvC,OAAO;QACLE,WAAW;YAACF;YAAMI,aAAI,CAACC,IAAI,CAACL,MAAM;SAAS;IAC7C;IAEA,MAAMM,QAAkB,EAAE;IAC1B,KAAK,MAAMC,aAAaT,WAAY;QAClC,KAAK,MAAMU,UAAUN,SAAU;YAC7BI,MAAMG,IAAI,CAAC,AAAGD,SAAO,MAAGD;QAC1B;IACF;IAEA,OAAOD;AACT"}